"""
S3 Storage backends - Only used in production
These classes are only imported when S3 storage is configured in production settings
"""
from storages.backends.s3boto3 import S3Boto3Storage
from django.conf import settings


class StaticStorage(S3Boto3Storage):
    location = settings.STATIC_LOCATION
    default_acl = "public-read"
    file_overwrite = True


class MediaStorage(S3Boto3Storage):
    location = settings.MEDIA_LOCATION
    default_acl = "public-read"  # keep uploads private unless you want public
    file_overwrite = True
    custom_domain = False    # signed URLs instead of public links
