import os
from datetime import timedelta
from .base import *

BASE_DIR = os.path.dirname(os.path.dirname(
    os.path.dirname(os.path.abspath(__file__))))
ALLOWED_HOSTS = config('ALLOWED_HOSTS', cast=lambda v: [
                       s.strip() for s in v.split(',')])

CORS_ORIGIN_ALLOW_ALL = True

# Test pic date and time
# USE_TZ = True


SIMPLE_JWT = {
    'ACCESS_TOKEN_LIFETIME': timedelta(hours=24),
    'AUTH_HEADER_TYPES': ('Bearer', 'JWT'),
}


# EMAIL CONFIG
""" AWS SES EMAIL SERVICE"""
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = config('EMAIL_HOST')
EMAIL_USE_TLS = config('EMAIL_USE_TLS', cast=bool)
EMAIL_PORT = config('EMAIL_PORT', cast=int)
EMAIL_USE_SSL = config('EMAIL_USE_SSL', cast=bool)
EMAIL_HOST_USER = config('EMAIL_HOST_USER')
EMAIL_HOST_PASSWORD = config('EMAIL_HOST_PASSWORD')
DEFAULT_FROM_EMAIL = config('DEFAULT_FROM_EMAIL')

"""BREEVO EMAIL SERVICE """
EMAIL_HOST_BREVO = config('EMAIL_HOST_BREVO')
EMAIL_PORT_BREVO = config('EMAIL_PORT_BREVO', cast=int)
EMAIL_HOST_USER_BREVO = config('EMAIL_HOST_USER_BREVO')
EMAIL_HOST_PASSWORD_BREVO = config('EMAIL_HOST_PASSWORD_BREVO')
BREEVO_API_KEY = config('BREEVO_API_KEY')

# Local file storage for staging (no S3)
STATIC_URL = '/static/'
MEDIA_URL = '/media/'

STATICFILES_DIRS = [
    os.path.join(BASE_DIR, "static"),
]

STATIC_ROOT = os.path.join(BASE_DIR, 'staticfiles')
MEDIA_ROOT = os.path.join(BASE_DIR, 'media')

# staging logging for debugging


REQUEST_LOGGING_SENSITIVE_HEADERS = [True]

LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '%(levelname)s %(asctime)s %(name)s.%(funcName)s:%(lineno)s- %(message)s'
        }
    },
    'handlers': {
        'logfile': {
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': os.path.join(BASE_DIR, 'server.log'),
            'maxBytes': 1024*1024*15,  # 15MB
            'backupCount': 10,
            'formatter': 'verbose'
        },
    },
    'loggers': {
        'django': {
            'handlers': ['logfile'],
            'level': 'WARNING'
        },
    },
}
