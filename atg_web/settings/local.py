from .base import *
from decouple import config
import os
# base_path = '/Users/<USER>/Desktop/sm/smart-eye-api'
BASE_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


#USE_TZ = True
ALLOWED_HOSTS = ['*']

CORS_ORIGIN_ALLOW_ALL = True

SIMPLE_JWT = {
    'ACCESS_TOKEN_LIFETIME': timedelta(days=30),
    'AUTH_HEADER_TYPES': ('Bearer', 'JWT')
}

""" AWS SES EMAIL SERVICE"""
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = config('EMAIL_HOST')
EMAIL_USE_TLS = config('EMAIL_USE_TLS', cast=bool)
EMAIL_PORT = config('EMAIL_PORT', cast=int)
EMAIL_USE_SSL = config('EMAIL_USE_SSL', cast=bool)
EMAIL_HOST_USER = config('EMAIL_HOST_USER')
EMAIL_HOST_PASSWORD = config('EMAIL_HOST_PASSWORD')
DEFAULT_FROM_EMAIL = config('DEFAULT_FROM_EMAIL')

"""BREEVO EMAIL SERVICE """
EMAIL_HOST_BREVO = config('EMAIL_HOST_BREVO')
EMAIL_PORT_BREVO = config('EMAIL_PORT_BREVO', cast=int)
EMAIL_HOST_USER_BREVO = config('EMAIL_HOST_USER_BREVO')
EMAIL_HOST_PASSWORD_BREVO = config('EMAIL_HOST_PASSWORD_BREVO')
BREEVO_API_KEY= config('BREEVO_API_KEY')




# EMAIL CONFIG test cicd test cicd
# EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'


PASSWORD_RESET_TIMEOUT_DAYS = 1

CELERY_BROKER_URL = CELERY_BROKER_URL = config('CELERY_BROKER_URL')
REQUEST_LOGGING_SENSITIVE_HEADERS = [True]


# INTERNAL_IPS = ("127.0.0.1",)
# INSTALLED_APPS += ("debug_toolbar",)  # noqa
# MIDDLEWARE.insert(0, "debug_toolbar.middleware.DebugToolbarMiddleware")

# LOGGING = {
#     'version': 1,
#     'disable_existing_loggers': False,
#     'handlers': {
#         'logfile': {
#             'class': 'logging.handlers.RotatingFileHandler',
#             'filename': os.path.join(BASE_DIR, 'server.log'),
          
#         },
#     },
#     'loggers': {
#         'django.request': {
#             'handlers': ['logfile'],
#             'level': 'DEBUG',  # change debug level as appropiate
#             'propagate': False,
#         },
#     },
# }