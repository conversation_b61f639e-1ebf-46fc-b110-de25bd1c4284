from datetime import timedelta
from .base import *

ALLOWED_HOSTS = config('ALLOWED_HOSTS', cast=lambda v: [
                       s.strip() for s in v.split(',')])


SIMPLE_JWT = {
    'ACCESS_TOKEN_LIFETIME': timedelta(days=30),
    'AUTH_HEADER_TYPES': ('Bearer', 'JWT')
}

CORS_ORIGIN_ALLOW_ALL = True

# EMAIL CONFIG

""" AWS SES EMAIL SERVICE"""
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = config('EMAIL_HOST')
EMAIL_USE_TLS = config('EMAIL_USE_TLS', cast=bool)
EMAIL_PORT = config('EMAIL_PORT', cast=int)
EMAIL_USE_SSL = config('EMAIL_USE_SSL', cast=bool)
EMAIL_HOST_USER = config('EMAIL_HOST_USER')
EMAIL_HOST_PASSWORD = config('EMAIL_HOST_PASSWORD')
DEFAULT_FROM_EMAIL = config('DEFAULT_FROM_EMAIL')

"""BREEVO EMAIL SERVICE """
EMAIL_HOST_BREVO = config('EMAIL_HOST_BREVO')
EMAIL_PORT_BREVO = config('EMAIL_PORT_BREVO', cast=int)
EMAIL_HOST_USER_BREVO = config('EMAIL_HOST_USER_BREVO')
EMAIL_HOST_PASSWORD_BREVO = config('EMAIL_HOST_PASSWORD_BREVO')
BREEVO_API_KEY = config('BREEVO_API_KEY')

# AWS S3 CONFIG - Production Only
AWS_ACCESS_KEY_ID = config("AWS_ACCESS_KEY_ID")
AWS_SECRET_ACCESS_KEY = config("AWS_SECRET_ACCESS_KEY")
AWS_STORAGE_BUCKET_NAME = config(
    "AWS_STORAGE_BUCKET_NAME", default="smdb-backup")
AWS_S3_REGION_NAME = config("AWS_S3_REGION_NAME", default="us-east-1")
AWS_S3_SIGNATURE_VERSION = "s3v4"
AWS_S3_OBJECT_PARAMETERS = {
    "CacheControl": "max-age=31536000, immutable",
}
AWS_DEFAULT_ACL = "public-read"

# Custom storage backends for S3
STATIC_LOCATION = "smarteye/static"
MEDIA_LOCATION = "smarteye/media"

STATICFILES_STORAGE = "atg_web.storage_backends.StaticStorage"
DEFAULT_FILE_STORAGE = "atg_web.storage_backends.MediaStorage"

AWS_S3_CUSTOM_DOMAIN = f"{AWS_STORAGE_BUCKET_NAME}.s3.{AWS_S3_REGION_NAME}.amazonaws.com"

STATIC_URL = f"https://{AWS_S3_CUSTOM_DOMAIN}/{STATIC_LOCATION}/"
MEDIA_URL = f"https://{AWS_S3_CUSTOM_DOMAIN}/{MEDIA_LOCATION}/"

LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '%(levelname)s %(asctime)s %(name)s.%(funcName)s:%(lineno)s- %(message)s'
        }
    },
    'handlers': {
        'logfile': {
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': os.path.join('server.log'),
            'maxBytes': 1024*1024*15,  # 15MB
            'backupCount': 10,
            'formatter': 'verbose'
        },
    },
    'loggers': {
        'django': {
            'handlers': ['logfile'],
            'level': 'WARNING'
        },
    },
}
