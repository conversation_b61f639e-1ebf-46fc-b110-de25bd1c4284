"""
Django settings for atg_web project.

Generated by 'django-admin startproject' using Django 2.0.9.

For more information on this file, see
https://docs.djangoproject.com/en/2.0/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/2.0/ref/settings/
"""
from sentry_sdk.integrations.redis import RedisIntegration
from sentry_sdk.integrations.celery import CeleryIntegration
from sentry_sdk.integrations.django import DjangoIntegration
import sentry_sdk
from corsheaders.defaults import default_headers
import os
from datetime import timed<PERSON><PERSON>
from decouple import config
# Build paths inside the project like this: os.path.join(BASE_DIR, ...)
import pymysql
pymysql.install_as_MySQLdb()

BASE_DIR = os.path.dirname(os.path.dirname(
    os.path.dirname(os.path.abspath(__file__))))


# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/2.0/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = config('SECRET_KEY')

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = config('DEBUG', cast=bool)

# Config for APM with sentry

CORS_ALLOW_HEADERS = list(default_headers) + [
    "Access-Control-Allow-Origin",
]
CORS_ORIGIN_ALLOW_ALL = True

sentry_sdk.init(
    dsn="https://<EMAIL>/1770167",
    integrations=[DjangoIntegration(), CeleryIntegration(),
                  RedisIntegration()],
    environment=config('ENVIRONMENT')
)

# Application definition

INSTALLED_APPS = [
    'django.contrib.auth',
    'django.contrib.admin',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    "channels",

    # Third-party Apps
    'rest_framework',
    'corsheaders',
    'drf_spectacular',

    # Local Apps
    'thirdparty',
    'backend.apps.BackendConfig',
    # 'test_without_migrations',
    'django_crontab',
    'auditlog',
    "storages",
]

MIDDLEWARE = [

    # 'request_logging.middleware.LoggingMiddleware',
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    'backend.custommiddleware.MyAuditCustomMiddleware',
    'auditlog.middleware.AuditlogMiddleware',
    'thirdparty.smart_tank.middleware.ThirdPartyMiddleware',
    # 'backend.request_log_middleware.RequestLogMiddleware',
]

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': config('DB_NAME'),
        'USER': config('DB_USER'),
        'PASSWORD': config('DB_PASSWORD'),
        'HOST': config('DB_HOST'),
        'PORT': config('DB_PORT'),

    },
    # Adding SQLite for duplicate data logging
    'sqlite_db': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': os.path.join(BASE_DIR, 'db.sqlite3'),
    },
    # 'station_manager': {
    #     'ENGINE': 'django.db.backends.mysql',
    #     'NAME': config('SM_DB_NAME'),
    #     'USER': config('SM_DB_USER'),
    #     'PASSWORD': config('SM_DB_PASSWORD'),
    #     'HOST': config('SM_DB_HOST'),
    #     'PORT': config('SM_DB_PORT'),
    # }

}

ROOT_URLCONF = 'atg_web.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [os.path.join(BASE_DIR, 'templates')],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'atg_web.wsgi.application'
ASGI_APPLICATION = "atg_web.asgi.application"
# USER MODEL
AUTH_USER_MODEL = 'backend.User'


# Password validation
# https://docs.djangoproject.com/en/2.0/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]


REST_FRAMEWORK = {
    'DEFAULT_PERMISSION_CLASSES': (
        'backend.permissions.IsActiveAuthenticated',
    ),
    'DEFAULT_AUTHENTICATION_CLASSES': ('rest_framework_simplejwt.authentication.JWTAuthentication',),
    'DEFAULT_SCHEMA_CLASS': 'drf_spectacular.openapi.AutoSchema',
    'EXCEPTION_HANDLER': 'backend.custom_exception_handler.my_exception_handler',
    'DATETIME_FORMAT': "%Y-%m-%d %H:%M:%S",
    # 'DATETIME_FORMAT': "%y-%m-%d %I:%M:%S %p",
    # 'DEFAULT_PAGINATION_CLASS': 'rest_framework.pagination.LimitOffsetPagination',
    # 'PAGE_SIZE': 5
    # 'DEFAULT_THROTTLE_RATES': {
    #     'anon': '1000/day',
    #     'user': '1000/day',
    #     'third_party': '1000/day',
    # },
    # 'DEFAULT_THROTTLE_CLASSES': (
    #     'rest_framework.throttling.AnonRateThrottle',
    #     'rest_framework.throttling.UserRateThrottle',
    # ),
}

# Internationalization
# https://docs.djangoproject.com/en/2.0/topics/i18n/

LANGUAGE_CODE = 'en-us'

TIME_ZONE = 'Africa/Lagos'

USE_I18N = True

USE_L10N = True

USE_TZ = False


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/2.0/howto/static-files/

# STATIC_URL = '/static/'

# STATICFILES_DIRS = [
#     os.path.join(BASE_DIR, "static"),
# ]


SPECTACULAR_SETTINGS = {
    'SCHEMA_PATH_PREFIX': r'/api/v[0-9]',
    'TITLE': 'Smarteye API',
    'DESCRIPTION': 'An interactive documentation of the entire smart-eye-api (each endpoint grouped into its module category).',
    'VERSION': '0.0.1',
    'SERVE_INCLUDE_SCHEMA': False,
    'SERVE_PERMISSIONS': [],
    "CACHE_SCHEMA": False,
}


CRONJOBS = [
    # ('0 23 * * *', 'backend.reports.cron.anomaly.main', '>> /var/log/anomaly.log'),
    # ('45 23 * * *', 'backend.reports.cron.missed_logs_anomaly_detector.main', '>> missed_log.log'),
    # ('0 */1 * * *', 'backend.reports.cron.latestlogfiller.main', '>> os.path.join(BASE_DIR, "crontab_logs/atg_logs.log")'),

]


CRONTAB_COMMAND_SUFFIX = '2>&1'


redis_host = config("REDIS_HOST", default="localhost")
redis_port = config("REDIS_PORT", default=6379, cast=int)
# CACHING BACKEND
CACHES = {
    "default": {
        "BACKEND": "django_redis.cache.RedisCache",
        "LOCATION": "redis://{}:{}".format(redis_host, redis_port),
    }
}

# Redis for channel layer
CHANNEL_LAYERS = {
    "default": {
        "BACKEND": "channels_redis.core.RedisChannelLayer",
        "CONFIG": {
            "hosts": [("127.0.0.1", 6379)],
        },
    },
}

# AWS S3 CONFIG
AWS_ACCESS_KEY_ID = config("AWS_ACCESS_KEY_ID")
AWS_SECRET_ACCESS_KEY = config("AWS_SECRET_ACCESS_KEY")
AWS_STORAGE_BUCKET_NAME = config(
    "AWS_STORAGE_BUCKET_NAME", default="smdb-backup")
AWS_S3_REGION_NAME = config("AWS_S3_REGION_NAME", default="eu-west-1")
AWS_S3_SIGNATURE_VERSION = "s3v4"
AWS_S3_OBJECT_PARAMETERS = {
    "CacheControl": "max-age=31536000, immutable",
}
AWS_DEFAULT_ACL = "public-read"
# If you use CloudFront or CDN
# AWS_S3_CUSTOM_DOMAIN = config(
#     "ASSETS_CDN_DOMAIN", default=f"{AWS_STORAGE_BUCKET_NAME}.s3.amazonaws.com")

# Custom storage backends
STATIC_LOCATION = "static"
MEDIA_LOCATION = "media"

STATICFILES_STORAGE = "atg_web.storage_backends.StaticStorage"
DEFAULT_FILE_STORAGE = "atg_web.storage_backends.MediaStorage"

AWS_S3_CUSTOM_DOMAIN = f"{AWS_STORAGE_BUCKET_NAME}.s3.{AWS_S3_REGION_NAME}.amazonaws.com"

STATIC_URL = f"https://{AWS_S3_CUSTOM_DOMAIN}/{STATIC_LOCATION}/"
MEDIA_URL = f"https://{AWS_S3_CUSTOM_DOMAIN}/{MEDIA_LOCATION}/"
