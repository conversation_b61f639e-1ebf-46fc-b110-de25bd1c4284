import os
import csv
import json

from django.db import connection
from django.shortcuts import get_object_or_404
from django.conf import settings

from rest_framework import status, generics
from rest_framework.views import APIView
from rest_framework.decorators import api_view

from drf_spectacular.utils import extend_schema

from backend import models
from .serializer import ProbeSerializer, ProbeChartResponseSerializer
from .. import utils
from .. import permissions


class ProbeList(generics.ListCreateAPIView):
    permission_classes = [(permissions.IsActiveAuthenticatedSuperAdminGroup |
                           permissions.IsActiveAuthenticatedProductAdmin | permissions.IsCompanyAdmin | permissions.ProbesModelPermission)]
    serializer_class = ProbeSerializer
    queryset = models.Probes.objects.all()

    def list(self, request, *args, **kwargs):
        response = super().list(request, *args, **kwargs)
        return utils.CustomResponse.Success(response.data)

    def create(self, request, *args, **kwargs):
        response = super().create(request, *args, **kwargs)
        return utils.CustomResponse.Success(response.data, response.status_code)


class ProbeDetails(generics.RetrieveUpdateDestroyAPIView):
    permission_classes = [(permissions.IsActiveAuthenticatedSuperAdminGroup |
                           permissions.IsActiveAuthenticatedProductAdmin | permissions.IsCompanyAdmin | permissions.ProbesModelPermission)]
    serializer_class = ProbeSerializer
    queryset = models.Probes.objects.all()

    def retrieve(self, request, *args, **kwargs):
        response = super().retrieve(request, *args, **kwargs)
        return utils.CustomResponse.Success(response.data, response.status_code)

    def update(self, request, *args, **kwargs):
        kwargs['partial'] = True
        response = super().update(request, *args, **kwargs)
        return utils.CustomResponse.Success(response.data, response.status_code)

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        return utils.CustomResponse.Success(response.data, response.status_code)


class ProbeChart(APIView):
    permission_classes = [(permissions.IsActiveAuthenticatedSuperAdminGroup |
                           permissions.IsActiveAuthenticatedProductAdmin | permissions.IsCompanyAdmin | permissions.ProbesModelPermission)]

    @extend_schema(responses=ProbeChartResponseSerializer)
    def get(self, request, *args, **kwargs):
        pk = kwargs.get('pk')
        chart = get_object_or_404(models.Probes, pk=pk).probe_chart
        if chart and chart.name:
            try:
                # Use storage-aware CSV conversion
                from backend.file_handler import convert_csv_to_json_from_storage
                json_chart = convert_csv_to_json_from_storage(chart)
                return utils.CustomResponse.Success(json_chart)
            except Exception as e:
                return utils.CustomResponse.Failure(f'Error reading chart: {str(e)}', status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        else:
            return utils.CustomResponse.Failure('No chart uploaded', status=status.HTTP_400_BAD_REQUEST)
