import csv
import redis
import json
import os

from django.conf import settings

from rest_framework import serializers
from rest_framework.validators import UniqueTogetherValidator, UniqueValidator
from backend import models
from ..sites.serializer import SiteSerializer
from .utils import is_valid_file_type

from ..utils import convert_csv_to_json
from decouple import config


class TankSerializer(serializers.ModelSerializer):
    Site = SiteSerializer(read_only=True)
    Site_id = serializers.PrimaryKeyRelatedField(
        queryset=models.Sites.objects.all(), source='Site', write_only=True)

    class Meta:
        model = models.Tanks
        fields = '__all__'
        validators = [
            UniqueTogetherValidator(
                queryset=model.objects.all(),
                fields=('Site', 'Tank_controller',
                        'Controller_polling_address', 'Tank_index'),
                message='Tank index already assigned'
            ),
            UniqueTogetherValidator(
                queryset=model.objects.all(),
                fields=['Site', 'Name'],
                message='A tank with this name already exists in this site'
            )
        ]
    '''
    Validate the following:
    1. Calibration_chart is valid file
    2. Site's number of tanks
    '''

    def validate_CalibrationChart(self, value):
        """Validate that the calibration chart is a valid CSV file with the required headers."""
        if not is_valid_file_type(value):
            raise serializers.ValidationError('Only csv files are allowed')

        # Validate headers
        try:
            # Handle InMemoryUploadedFile or TemporaryUploadedFile
            file = value.file
            file.seek(0)  # Reset file pointer to the beginning
            reader = csv.reader(file.read().decode('utf-8').splitlines())
            headers = next(reader)
        except Exception as e:
            raise serializers.ValidationError(
                f'Error reading CSV file: {str(e)}')

        expected_headers = ['Height(mm)', 'Volume(ltrs)']
        if headers != expected_headers:
            raise serializers.ValidationError(
                f"CSV header must be exactly: {', '.join(expected_headers)}")

        return value

    def validate_Site(self, value):
        if value.Owned:
            raise serializers.ValidationError('Site is owned by company')
        return value

    def create(self, validated_data):
        """Create a new tank after validating site tank limits."""
        site = validated_data.get('Site')
        if site and site.tank_count >= site.Number_of_tanks:
            raise serializers.ValidationError({
                'site': 'Tank Site has reached limit for registered number of tanks'
            })
        return models.Tanks.objects.create(**validated_data)

    def _create_latest_atg_log(self, tank):
        """Create or update a LatestAtgLog entry for the tank."""
        try:
            models.LatestAtgLog.objects.create(
                Tank_id=tank.Tank_id,
                Tank_name=tank.Name,
                Height=tank.Tank_height,
                siteName=tank.Site,
                Capacity=tank.Capacity,
                Unit=tank.UOM,
                DisplayUnit=tank.Display_unit,
                Product=tank.Product,
                Tank_controller=tank.Tank_controller,
                Reorder=tank.Reorder,
                LL_Level=tank.LL_Level,
                HH_Level=tank.HH_Level,
                Tank_Note=tank.Tank_Note,
            )
        except Exception as e:
            print(f"Create failed, attempting update instead: {str(e)}")
            try:
                site = models.Sites.objects.get(Site_id=tank.Site.Site_id)
                product = models.Products.objects.get(
                    Product_id=tank.Product.Product_id)
                models.LatestAtgLog.objects.update_or_create(
                    Tank_id=tank.Tank_id,
                    defaults={
                        "Tank_name": tank.Name,
                        "Height": tank.Tank_height,
                        "siteName": site.Name,
                        "Capacity": tank.Capacity,
                        "Unit": tank.UOM,
                        "DisplayUnit": tank.Display_unit,
                        "Product": product.Name,
                        "Tank_controller": tank.Tank_controller,
                        "Reorder": tank.Reorder,
                        "LL_Level": tank.LL_Level,
                        "HH_Level": tank.HH_Level,
                        "Tank_Note": tank.Tank_Note,
                    },
                )
            except Exception as e2:
                print(f"Update also failed: {str(e2)}")

    def _update_calibration_chart_redis(self, tank):
        """Update the calibration chart in Redis."""
        if not tank.CalibrationChart:
            return

        key = f'Tank{tank.pk}'
        redis_client = redis.Redis(
            host=config('REDIS_HOST'),
            port=config('REDIS_PORT'),
            db=0,
            charset="utf-8",
            decode_responses=True
        )

        try:
            chart_path = os.path.join(
                settings.MEDIA_ROOT, tank.CalibrationChart.name)
            json_chart = convert_csv_to_json(chart_path)
            redis_client.hset('tanks_calibration_chart',
                              key, json.dumps(json_chart))
        except Exception as e:
            print(f"Failed to update calibration chart in Redis: {str(e)}")
        finally:
            redis_client.close()

    def save(self, **kwargs):
        """Save the tank and handle related operations."""
        tank = super().save(**kwargs)
        self._create_latest_atg_log(tank)
        self._update_calibration_chart_redis(tank)
        return tank


class GetTankIDsSerializer(serializers.Serializer):
    site = serializers.IntegerField()


class GetTankResponseSerializer(serializers.ModelSerializer):
    class Meta:
        model = models.Tanks
        fields = ['Tank_id', 'Name']


class TankByIdSerializer(serializers.ModelSerializer):

    class Meta:
        model = models.Tanks
        fields = '__all__'
