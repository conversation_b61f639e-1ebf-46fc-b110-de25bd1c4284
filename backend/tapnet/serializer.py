import redis
import json
import os

from django.conf import settings

from rest_framework import serializers
from rest_framework.validators import UniqueTogetherValidator
from backend import models
from ..sites.serializer import SiteSerializer
from .utils import is_valid_file_type

from ..utils import convert_csv_to_json
from decouple import config


class TankSerializer(serializers.ModelSerializer):
    Site = SiteSerializer(read_only=True)
    Site_id = serializers.PrimaryKeyRelatedField(
        queryset=models.Sites.objects.all(), source='Site', write_only=True)

    class Meta:
        model = models.Tanks
        fields = '__all__'
        validators = [
            UniqueTogetherValidator(
                queryset=model.objects.all(),
                fields=('Site', 'Tank_controller',
                        'Controller_polling_address', 'Tank_index'),
                message='Tank index already assigned'
            )
        ]
    '''
    Validate the following:
    1. Calibration_chart is valid file
    2. Site's number of tanks
    '''

    def validate_CalibrationChart(self, value):
        if not is_valid_file_type(value):
            raise serializers.ValidationError('Only csv files are allowed')
        return value

    def create(self, validated_data):
        # if tank's site has exceeded number of tanks, raise error
        site = validated_data.get('Site', None)
        if site and site.tank_count >= site.Number_of_tanks:
            raise serializers.ValidationError(
                'Tank Site has reached limit for registered number of tanks')
        return models.Tanks.objects.create(**validated_data)

    def save(self, **kwargs):
        tank = super().save(**kwargs)
        try:
            models.LatestAtgLog.objects.create(
                Tank_id=tank.Tank_id,
                Tank_name=tank.Name,
                Height=tank.Tank_height,
                siteName=tank.Site,
                Capacity=tank.Capacity,
                Unit=tank.UOM,
                DisplayUnit=tank.Display_unit,
                Product=tank.Product,
                Tank_controller=tank.Tank_controller,
                Reorder=tank.Reorder,
                LL_Level=tank.LL_Level,
                HH_Level=tank.HH_Level,
                Tank_Note=tank.Tank_Note,
            )
        except:
            pass

        # if tank CalibrationChart is set, update redis value
        if tank.CalibrationChart and tank.CalibrationChart.name:
            # inside redis
            key = 'Tank'+str(tank.pk)
            red = redis.Redis(host=config('REDIS_HOST'), port=config(
                'REDIS_PORT'), db=0, charset="utf-8", decode_responses=True)
            try:
                # Use storage-aware CSV conversion
                from backend.file_handler import convert_csv_to_json_from_storage
                json_chart = convert_csv_to_json_from_storage(
                    tank.CalibrationChart)
            except Exception as err:
                print(err)
            else:
                # setting tank chart
                red.hset('tanks_calibration_chart',
                         key, json.dumps(json_chart))
        return tank


class GetTankIDsSerializer(serializers.Serializer):
    site = serializers.IntegerField()


class GetTankResponseSerializer(serializers.ModelSerializer):
    class Meta:
        model = models.Tanks
        fields = ['Tank_id', 'Name']


class TankByIdSerializer(serializers.ModelSerializer):

    class Meta:
        model = models.Tanks
        fields = '__all__'
