import redis
import pickle
import csv

from concurrent.futures import ThreadPoolExecutor
from typing import List
import datetime as dt

from backend import models

from .. import utils
from . import queries as q
from decouple import config
# test
'''
(local_id=d[0], read_at=d[1],
                                 pv=d[2], pv_flag=d[3], sv=d[4], device_address=d[5], multicont_polling_address=d[6],
                                 tank_index=d[7], temperature=d[8],
                                 controller_type=d[9]) 
'''


def filter_for_latest_logs(logs):
    # use a set to hold tank refs whose log have been seen
    seen_tanks = set()
    filtered_logs = []
    for log in logs:
        tank_ref = '{}-{}-{}-{}'.format(log[5], log[6],
                                        log[7], log[-1])

        if tank_ref in seen_tanks:
            continue
        else:
            seen_tanks.add(tank_ref)
        filtered_logs.append(log)
    return filtered_logs


def update_tank_records(logs):
    for log in logs:
        # get conversion strategy
        strategy = ConversionFactory().create_conversion_strategy(
            log['Unit'],
            log['Display Unit']
        )
        # convert using the strategy
        log = strategy().convert(log)
    return logs


def update_tankgroup_records(logs):
    # perform tank level conversion
    logs = update_tank_records(logs)
    for log in logs:
        log = TankgroupConversion().convert(log)
    return logs


def compute_pv(mac_address, tank_height, tank_index):
    # get tank_chart using tank_index
    offset_height = 0  # Initialize default values
    density = 1

    try:
        from .. import models
        from ..file_handler import convert_csv_to_json_from_storage

        # Get the tank object using the same logic as the SQL query
        tank = models.Tanks.objects.select_related('Site__Device').filter(
            Site__Device__Device_unique_address=mac_address,
            Tank_index=tank_index
        ).first()

        if not tank:
            raise Exception("Tank not found")

        offset_height = tank.Offset if tank.Offset else 0
        density = tank.Density if tank.Density is not None else 1
        chart = []

        # get the tank_chart to chart memory using storage-aware method
        if tank.CalibrationChart and tank.CalibrationChart.name:
            chart_data = convert_csv_to_json_from_storage(
                tank.CalibrationChart)
            for each_line in chart_data:
                chart.append((each_line['Height(mm)'],
                             each_line['Volume(ltrs)']))
        else:
            raise Exception("No calibration chart found")

    except Exception as e:
        return float(0.0), float(tank_height) + float(offset_height), e

    # compute tank volume from the tank_height(sv) using the tank calibration chart
    counter = 0
    for each in chart:
        if float(each[0]) == float(tank_height) + float(offset_height):
            pv = float(each[1])
            break
        if float(each[0]) >= float(tank_height):
            uH, uV, lH, lV, sv = float(chart[counter-1][0]), float(chart[counter-1][1]), float(
                each[0]), float(each[1]), float(tank_height) + float(offset_height)
            pv = (((abs(sv - uH) * abs(lV - uV)) / (abs(lH - uH))) + uV)
            break
        counter += 1

    try:
        return float(pv * density), float(sv), "Done"
    except Exception as e:
        return float(0.0), float(tank_height) + float(offset_height), e


class ConversionFactory:
    def create_conversion_strategy(self, unit, display):
        if unit != display:
            if unit == 'm3' and display == 'L':
                return MeterCubeToLitres
            if unit == 'L' and display == 'gal':
                return LitresToGallon

            if unit == 'T' and display == 'L':
                return TonnesToLitres
        else:
            return SameUnit


class ConversionStrategy:
    def convert(self, log):
        raise NotImplementedError('Needs to subclass this class')


class TonnesToLitres(ConversionStrategy):
    # [*] waiting for an update from Site guys to know the right formular
    # to use in computing this
    def convert(self, log):
        log["Volume"] = "{0:.3f}".format(float(log["Volume"]))
        return log


class MeterCubeToLitres(ConversionStrategy):
    def convert(self, log):
        log["Volume"] = "{0:.3f}".format(float(log["Volume"])*1000)
        return log


class LitresToGallon(ConversionStrategy):
    def convert(self, log):
        log["Volume"] = "{0:.3f}".format(float(log["Volume"])/3.78541)
        log["Height"] = "{0:.3f}".format(float(log["Height"])/25.4)
        return log


class SameUnit(ConversionStrategy):
    def convert(self, log):
        log["Volume"] = "{0:.3f}".format(float(log["Volume"]))
        log["Height"] = "{0:.3f}".format(float(log["Height"]))
        return log


class TankgroupConversion(ConversionStrategy):
    def convert(self, log):
        volume = float(log["Volume"])
        capacity = log["Capacity"]

        log["Fill %"] = round(((volume / capacity) * 100), 2)
        log["tankVolume"] = "{0:.3f}".format(float(log["Volume"]))
        log["tankHeight"] = "{0:.3f}".format(float(log["Height"]))
        if "temperature" in log:
            log["temperature"] = "{0:.3f}".format(float(log["temperature"]))
        if "water" in log:
            log["water"] = "{0:.3f}".format(float(log["water"]))
        return log


class SqlLogsPaginationCacheMixin:
    def paginate_and_cache(self, request, *args, **kwargs):
        '''
        Apply caching
        '''
        # check for key: requesturl+ids+start+end
        key = (request.get_full_path()+'_ids:{}'+'_tank_ids:{}'+'_start:{}' +
               '_end:{}').format(self.ids, self.tank_ids, self.start_date, self.end_date)
        redis_instance = redis.Redis(host=config(
            'REDIS_HOST'), port=config('REDIS_PORT'), db=0)
        client_count = request.data.get('count', None)
        pickled_response = redis_instance.get(key)
        if pickled_response:
            try:
                response = pickle.loads(pickled_response)
                content = response['content']
                headers = response['headers']
            except:
                pass
            else:
                return utils.CustomResponse.Success(data=content, headers=headers)

        if hasattr(self, 'pagination_class') and self.pagination_class is not None:
            limit = self.pagination_class().default_limit
            offset = self.pagination_class().get_offset(request)
        else:
            limit = config('ATG_LOGS_QUERY_LIMIT', cast=int)
            offset = 0
        if self.ids:
            '''
            Use threading to get count, and data
            '''

            count_query_fn = kwargs.pop('count_query', None)
            data_query_fn = kwargs.pop('data_query', None)
            with ThreadPoolExecutor(max_workers=3) as executor:
                result = executor.submit(
                    data_query_fn,
                    self.ids,
                    self.tank_ids,
                    self.start_date,
                    self.end_date,
                    limit,
                    offset
                )
                if client_count is None:
                    result_count = executor.submit(
                        count_query_fn,
                        self.ids,
                        self.tank_ids,
                        self.start_date,
                        self.end_date,
                        limit,
                        offset
                    )
                else:
                    result_count = client_count
            try:
                result_count = result_count.result()
            except AttributeError:
                result_count = client_count

            result = result.result()
            # update records
            result = update_tank_records(result)
            if self.paginator:
                self.paginator.set_log_count(result_count)
            page = self.paginate_queryset(result)
            if page:
                data, headers = self.get_paginated_response(result)
            else:
                data = result
                headers = {}
            '''
            set response in cache
            '''
            cache_response_payload = {
                'content': data,
                'headers': headers
            }
            pickled_response = pickle.dumps(cache_response_payload)
            redis_instance.set(key, pickled_response, 5*60)
            return utils.CustomResponse.Success(data=data, headers=headers)


class SqlLogsPaginationCacheMixin2:
    def paginate_and_cache2(self, request, *args, **kwargs):
        '''
        Apply caching
        '''
        # check for key: requesturl+ids+start+end
        # key = (request.get_full_path()+'_ids:{}'+'_tank_ids:{}'+'_start:{}'+'_end:{}').format(self.ids,self.tank_ids,self.start_date,self.end_date)
        key = (request.path+'_ids:{}'+'_tank_ids:{}'+'_start:{}' +
               '_end:{}').format(self.ids, self.tank_ids, self.start_date, self.end_date)
        redis_instance = redis.Redis(host=config(
            'REDIS_HOST'), port=config('REDIS_PORT'), db=0)
        client_count = request.data.get('count', None)
        pickled_response = redis_instance.get(key)
        if pickled_response:
            try:
                response = pickle.loads(pickled_response)
                content = response['content']
                headers = response['headers']
            except:
                pass
            else:
                return utils.CustomResponse.Success(data=content, headers=headers)

        if hasattr(self, 'pagination_class') and self.pagination_class is not None:
            limit = self.pagination_class().default_limit
            offset = self.pagination_class().get_offset(request)
        else:
            limit = config('ATG_LOGS_QUERY_LIMIT', cast=int)
            offset = 0
        if self.ids:
            '''
            Use threading to get count, and data
            '''

            count_query_fn = kwargs.pop('count_query', None)
            data_query_fn = kwargs.pop('data_query', None)
            with ThreadPoolExecutor(max_workers=3) as executor:
                result = executor.submit(
                    data_query_fn,
                    self.ids,
                    self.tank_ids,
                    self.start_date,
                    self.end_date,
                    limit,
                    offset
                )
                if client_count is None:
                    result_count = executor.submit(
                        count_query_fn,
                        self.ids,
                        self.tank_ids,
                        self.start_date,
                        self.end_date,
                        limit,
                        offset
                    )
                else:
                    result_count = client_count
            try:
                result_count = result_count.result()
            except AttributeError:
                result_count = client_count

            result = result.result()
            # update records
            result = update_tank_records(result)
            if self.paginator:
                self.paginator.set_log_count(result_count)
            page = self.paginate_queryset(result)
            if page:
                data, headers = self.get_paginated_response(result)
            else:
                data = result
                headers = {}
            '''
            set response in cache
            '''
            cache_response_payload = {
                'content': data,
                'headers': headers
            }
            pickled_response = pickle.dumps(cache_response_payload)
            redis_instance.set(key, pickled_response, 5*60)
            return utils.CustomResponse.Success(data=data, headers=headers)


class RevampedSqlLogsPaginationCacheMixin:
    def paginate_and_cache(self, request, *args, **kwargs):
        '''
        Apply caching
        '''
        # check for key: requesturl+ids+start+end
        key = (request.get_full_path()+'_tank_ids:{}'+'_start:{}' +
               '_end:{}').format(self.tank_ids, self.start_date, self.end_date)
        redis_instance = redis.Redis(host=config(
            'REDIS_HOST'), port=config('REDIS_PORT'), db=0)
        pickled_response = redis_instance.get(key)
        if pickled_response:
            try:
                response = pickle.loads(pickled_response)
                content = response['content']
                headers = response['headers']
            except:
                pass
            else:
                return utils.CustomResponse.Success(data=content, headers=headers)
        if hasattr(self, 'pagination_class') and self.pagination_class is not None:
            limit = self.pagination_class().default_limit
            offset = self.pagination_class().get_offset(request)
        else:

            # limit = 10000000
            limit = config('ATG_LOGS_QUERY_LIMIT', cast=int)
            offset = 0

        if self.tank_ids:
            '''
            Use threading to get data
            '''
            data_query_fn = kwargs.pop('data_query', None)
            with ThreadPoolExecutor(max_workers=2) as executor:

                result = executor.submit(
                    data_query_fn,
                    self.tank_ids,
                    self.start_date,
                    self.end_date,
                    limit,
                    offset
                )
            result = result.result()

            # update records
            result = update_tank_records(result)
            data = result
            headers = {}
            '''
            set response in cache
            '''
            cache_response_payload = {
                'content': data,
                'headers': headers
            }
            pickled_response = pickle.dumps(cache_response_payload)
            redis_instance.set(key, pickled_response, 5*60)
            return utils.CustomResponse.Success(data=data, headers=headers)


def getSitesAndTanks(site_ids):
    result = q.get_tanks_in_site(site_ids)
    print(result)
    site_device_address = q.get_site_device_address(site_ids)
    print(site_device_address)
    for each_site in result:
        # r = redis.Redis(host=config('REDIS_HOST'), port=config('REDIS_PORT'), db=0)
        try:
            # last_read = r.get(str(site_device_address[each_site['site_name']])).decode('utf-8')
            time_diff = dt.datetime.now(
            ) - site_device_address[0]['last_seen']  # type: ignore
            # status if last_read < 24 hours
            if (time_diff) < dt.timedelta(hours=24):
                each_site['status'] = "online"
            elif (time_diff) < dt.timedelta(hours=48):
                each_site['status'] = "Offline"
            else:
                each_site['status'] = "inactive"
        except Exception as e:
            each_site['status'] = "inactive"
    return result


def date_time_conversion(transaction_time):
    try:
        get_day = dt.datetime.strptime(
            str(transaction_time),  "%d/%m/%Y %H:%M").day
        get_month = dt.datetime.strptime(
            str(transaction_time),  "%d/%m/%Y %H:%M").month
        get_year = dt.datetime.strptime(
            str(transaction_time),  "%d/%m/%Y %H:%M").year
        hr = dt.datetime.strptime(
            str(transaction_time),  "%d/%m/%Y %H:%M").hour
        mn = dt.datetime.strptime(
            str(transaction_time),  "%d/%m/%Y %H:%M").minute
        sec = dt.datetime.strptime(
            str(transaction_time),  "%d/%m/%Y %H:%M").second
        date_time_str = f"{get_day:02}/{get_month:02}/{get_year:02} {hr:02}:{mn:02}:{sec:02}"
        return dt.datetime.strptime(str(date_time_str), '%d/%m/%Y %H:%M:%S')
    except:
        get_day = dt.datetime.strptime(
            str(transaction_time),  "%Y-%m-%d %H:%M:%S").day
        get_month = dt.datetime.strptime(
            str(transaction_time),  "%Y-%m-%d %H:%M:%S").month
        get_year = dt.datetime.strptime(
            str(transaction_time),  "%Y-%m-%d %H:%M:%S").year
        hr = dt.datetime.strptime(
            str(transaction_time),  "%Y-%m-%d %H:%M:%S").hour
        mn = dt.datetime.strptime(
            str(transaction_time),  "%Y-%m-%d %H:%M:%S").minute
        sec = dt.datetime.strptime(
            str(transaction_time),  "%Y-%m-%d %H:%M:%S").second
        date_time_str = f"{get_day:02}/{get_month:02}/{get_year:02} {hr:02}:{mn:02}:{sec:02}"
        return dt.datetime.strptime(str(date_time_str), '%d/%m/%Y %H:%M:%S')


def determine_pv(volume, tank_id):

    try:
        old_flag = models.LatestAtgLog.objects.get(Tank_id=tank_id).Volume

        if float(volume) < (float(old_flag) - float(5.0)):
            pv_flag = 2
        elif float(volume) > (float(old_flag) + float(10.0)):
            pv_flag = 3
        else:
            pv_flag = 1
        return pv_flag
    except:
        pv_flag = 1
        return pv_flag
