from django.urls import path
from . import views

urlpatterns = [
    path('data_logger/', views.DataLogger.as_view(), name='data_logger'),
    path('pic_data_logger/', views.PicDataLogger.as_view(), name='pic_data_logger'),
    path('pic_logger_confirm/', views.PicDataLoggerConfirm.as_view(),
         name='pic_logger_confirm'),
    path('delivery_logger/', views.DeliveryLogger.as_view(),
         name='tls_delivery_logger'),
    path('sensor_data_logger/', views.SensorDataLogger.as_view(),
         name='sensor_data_logger'),
    path('smarthub/data_logger/', views.SmarthubLogsLogger.as_view(),
         name='smarthub_data_logger'),
    path('smarthub/deliveries_logger/', views.SmarthubDeliveriesLogger.as_view(),
         name='smarthub_deliveries_logger'),
    path('revampedtanklogs/', views.RevampedTankReadings.as_view()),
    path('tanklogs/', views.TankReadings.as_view()),
    path('anomaly/', views.AnomalyTankReadingReport.as_view()),  # Reading anomaly
    path('revampedtankreading/latest/', views.RevampedCurrentTankDetails.as_view(),
         name="revamped_latest_tank_reading"),
    #     path('tank/latest/', views.LatestTankDetails.as_view(),
    #          name="revamped_latest_tank_reading"),
    path('tankreading/latest/', views.CurrentTankDetails.as_view(),
         name="latest_tank_reading"),
    path('modifiedtankreading/latest/', views.ModifiedCurrentTankDetails.as_view(),
         name="modified_latest_tank_reading"),
    path('tankgrouptanklogs/', views.TankReadingsForTankGroups.as_view()),
    path('tankreading/tankgroup/latest/',
         views.CurrentTankDetailsForTankgroup.as_view()),
    path('revampedtankreading/tankgroup/latest/',
         views.RevampedCurrentTankDetailsForTankgroup.as_view()),
    path('tank/<int:pk>/recent-logs/', views.SpecificTankReading.as_view()),
    path('tank_map/<str:site_ids>/', views.TankMap.as_view(), name='tank_map'),
    path('atg_tls_remote_config/', views.AtgTlsConfig.as_view(),
         name='atg_tls_remote_config'),
    path('atg_tls_logs/', views.AtgTlsLogs.as_view(), name='atg_tls_logs'),
    path('atg_tls_delivery/', views.PicTlsDeliveryLogger.as_view(),
         name='atg_tls_delivery'),
    path('sensor_chart_config/', views.SensorChartConfig.as_view(),
         name='sensor_chart_config'),
    path('atg_hls_logs_confirm/', views.ConfirmAtgHlsLogs.as_view(),
         name="confirm_atg_hls_logs"),
    path('atg_hls_logs/', views.AtgHlsLogs.as_view(), name="atg_hls_logs"),
    path('atg_hls_remote_config', views.AtgHlsConfig.as_view(),
         name="atg_hls_remote_config"),
    path('tank-logs-new/', views.TankReadingsNewApproach.as_view(),
         name='tank_logs_new'),
]
