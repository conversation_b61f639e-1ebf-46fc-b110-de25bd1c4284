import datetime
from django.db import connection
from backend import models
from django.db.models import Q

from . import utils as log_utils


def dictfetchall(cursor):
    columns = [col[0] for col in cursor.description]
    return [dict(zip(columns, row)) for row in cursor.fetchall()]


def format_to_dict(result):
    data = {}
    for each in result:
        data[each['Name']] = each['Device']
    return data


def formatter(result):
    sites = set()
    tanks = set()
    return_result = []
    temp_data = {}
    for each in result:
        sites.add(each['site_name'])

    for site in sites:
        for data in result:
            if data['site_name'] == site and temp_data.get('site_name') == None and data['tank'] not in tanks:
                temp_data['site_name'] = data['site_name']
                temp_data['longitude'] = data['longitude']
                temp_data['latitude'] = data['latitude']
                temp_data['tanks'] = [[data['tank']]]
                tanks.add(data['tank'])
                continue
            if data['site_name'] == site and temp_data.get('site_name') == site and data['tank'] not in tanks:
                temp_data['tanks'].append([data['tank']])
                tanks.add(data['tank'])

        return_result.append(temp_data)
        temp_data = {}

    return return_result


def get_tanklogs_count(site_ids, tank_ids, start, end, limit, offset):
    with connection.cursor() as c:
        query = """
                SELECT 
                    COUNT(l.pv)
                FROM
                    (atg_primary_log l
                    JOIN (backend_sites s
                    JOIN backend_devices d ON s.Device_id = d.Device_id) ON l.device_address = d.Device_unique_address
                    JOIN backend_tanks t ON s.Site_id = t.Site_id
                        AND l.multicont_polling_address = t.Controller_polling_address
                        AND l.tank_index = t.Tank_index AND t.Tank_controller = l.controller_type)
                WHERE
                    s.Site_id IN %s
                        AND t.Tank_id IN %s
                        AND 0+l.pv BETWEEN 0.1 AND 1000000
                        AND l.read_at BETWEEN %s AND %s
                        AND l.flag_log = 1
                ORDER BY l.read_at DESC
                LIMIT %s, %s;
        """
        c.execute(query, [tuple(site_ids), tuple(
            tank_ids), start, end, offset, limit])
        data = c.fetchone()[0]
    return data


def get_tanklogs(site_ids, tank_ids, start, end, limit, offset):
    with connection.cursor() as c:
        query = """
                SELECT 
                    s.Name AS 'Site Name',
                    s.Site_id as 'siteId',
                    t.Name AS 'Tank Name',
                    t.Tank_id AS 'Tank_id',
                    t.Capacity AS 'Tank Capacity',
                    t.UOM AS 'Unit',
                    t.Display_unit AS 'Display Unit',
                    l.multicont_polling_address AS 'Controller polling address',
                    l.tank_index AS 'Tank index',
                    l.pv AS 'Volume',
                    ROUND(l.sv,2) AS 'Height',
                    l.read_at AS 'Log Time',
                    l.controller_type AS 'Controller_type'
                FROM
                    (atg_primary_log l force INDEX (indx_read_at) 
                    JOIN (backend_sites s
                    JOIN backend_devices d ON s.Device_id = d.Device_id) ON l.device_address = d.Device_unique_address
                    JOIN backend_tanks t ON s.Site_id = t.Site_id
                        AND l.multicont_polling_address = t.Controller_polling_address
                        AND l.tank_index = t.Tank_index AND t.Tank_controller = l.controller_type)
                WHERE
                    s.Site_id IN %s
                        AND t.Tank_id IN %s
                        AND 0+l.pv BETWEEN 0.1 AND 1000000
                        AND l.read_at BETWEEN %s AND %s
                ORDER BY l.read_at DESC
                LIMIT %s, %s;
        """
        c.execute(query, [tuple(site_ids), tuple(
            tank_ids), start, end, offset, limit])
        data = dictfetchall(c)
    return data


def get_anomaly_logs():
    with connection.cursor() as c:
        query = """
                SELECT 
                    s.Name AS 'Site Name',
                    s.Site_id as 'siteId',
                    t.Name AS 'Tank Name',
                    t.Tank_id AS 'Tank_id',
                    l.read_at AS 'Log Time',
                FROM
                    (atg_primary_log l force INDEX (indx_read_at) 
                    JOIN (backend_sites s
                    JOIN backend_devices d ON s.Device_id = d.Device_id) ON l.device_address = d.Device_unique_address
                    JOIN backend_tanks t ON s.Site_id = t.Site_id
                        AND l.multicont_polling_address = t.Controller_polling_address
                        AND l.tank_index = t.Tank_index AND t.Tank_controller = l.controller_type)
                WHERE
                    s.Site_id IN %s
                        AND t.Tank_id IN %s
                        AND 0+l.pv BETWEEN 0.1 AND 1000000
                        AND l.read_at BETWEEN %s AND %s
                        AND l.flag_log = 1
                ORDER BY l.read_at DESC
                LIMIT %s, %s;
        """
        c.execute(query)
        data = dictfetchall(c)
    return data


def revamped_get_tanklogs(tank_ids, start, end, limit, offset):
    with connection.cursor() as c:
        query = """
                    SELECT 
                        l.siteName AS 'Site Name',
                        l.Site_id as 'siteId',
                        l.Tank_name AS 'Tank Name',
                        l.Tank_id AS 'Tank_id',
                        l.Capacity AS 'Tank Capacity',
                        l.UOM AS 'Unit',
                        l.Display_unit AS 'Display Unit',
                        l.multicont_polling_address AS 'Controller polling address',
                        l.tank_index AS 'Tank index',
                        l.pv AS 'Volume',
                        l.sv AS 'Height',
                        l.read_at AS 'Log Time',
                        l.controller_type AS 'Controller_type'
                    FROM 
                        atg_primary_log l
                    WHERE
                            l.Tank_id IN %s
                            AND l.read_at BETWEEN %s AND %s
                            AND l.flag_log = 1
                    ORDER BY l.read_at DESC
                    LIMIT %s, %s;
                """
        c.execute(query, [tuple(tank_ids), start, end, offset, limit])
        data = dictfetchall(c)
    return data


def get_specific_tank_reading(tank_id):
    with connection.cursor() as c:
        query = """
                SELECT 
                    s.Name AS 'Site Name',
                    s.Site_id as 'siteId',
                    t.Name AS 'Tank Name',
                    t.Tank_id AS 'Tank_id',
                    t.UOM AS 'Unit',
                    t.Display_unit AS 'Display Unit',
                    l.pv AS 'Volume',
                    l.sv AS 'Height',
                    t.Offset AS 'Current Offset value',
                    l.read_at AS 'Log Time'
                FROM
                    (atg_primary_log l
                    JOIN (backend_sites s
                    JOIN backend_devices d ON s.Device_id = d.Device_id) ON l.device_address = d.Device_unique_address
                    JOIN backend_tanks t ON s.Site_id = t.Site_id
                        AND l.multicont_polling_address = t.Controller_polling_address
                        AND l.tank_index = t.Tank_index
                        AND t.Tank_controller = l.controller_type)
                WHERE
                    t.Tank_id = %s
                    AND
                    0+l.pv BETWEEN 0.1 AND 1000000
                    AND l.flag_log = 1
                ORDER BY l.read_at DESC
                LIMIT 5;
        """
        c.execute(query, [tank_id])
        data = dictfetchall(c)
    return data


def get_tankgrouplogs_count(tankgroup_ids, start, end, limit, offset):
    with connection.cursor() as c:
        query = """
                SELECT
                    COUNT(l.pv) 
                FROM
                    (atg_primary_log l
                    JOIN (backend_sites s
                    JOIN backend_devices d ON s.Device_id = d.Device_id) ON l.device_address = d.Device_unique_address
                    JOIN backend_tanks t ON s.Site_id = t.Site_id
                        AND l.multicont_polling_address = t.Controller_polling_address
                        AND l.tank_index = t.Tank_index AND t.Tank_controller = l.controller_type)
                    JOIN backend_tankgroups_Tanks tgt ON tgt.tanks_id = t.Tank_id
                    JOIN backend_tankgroups tg  ON tg.Group_id = tgt.tankgroups_id
                WHERE
                    tgt.tankgroups_id IN %s
                        AND 0+l.pv BETWEEN 0.1 AND 1000000
                        AND l.read_at BETWEEN %s AND %s
                        AND l.flag_log = 1
                ORDER BY l.read_at DESC
                LIMIT %s, %s;
        """
        c.execute(query, [tuple(tankgroup_ids), start, end, offset, limit])
        data = c.fetchone()[0]
    return data


def get_tankgroup_logs(tankgroup_ids, start, end, limit, offset):
    with connection.cursor() as c:
        query = """
                SELECT 
                    tg.Name AS 'TankGroup Name',
                    tg.Group_id AS 'TankGroupId',
                    s.Name AS 'Site Name',
                    s.Site_id as 'siteId',
                    t.Name AS 'Tank Name',
                    t.Tank_id AS 'Tank_id',
                    t.Capacity AS 'Tank Capacity',
                    t.UOM AS 'Unit',
                    t.Display_unit AS 'Display Unit',
                    l.multicont_polling_address AS 'Controller polling address',
                    l.tank_index AS 'Tank index',
                    l.pv AS 'Volume',
                    l.sv AS 'Height',
                    l.read_at AS 'Log Time',
                    l.controller_type AS 'Controller_type'
                FROM
                    (atg_primary_log l
                    JOIN (backend_sites s
                    JOIN backend_devices d ON s.Device_id = d.Device_id) ON l.device_address = d.Device_unique_address
                    JOIN backend_tanks t ON s.Site_id = t.Site_id
                        AND l.multicont_polling_address = t.Controller_polling_address
                        AND l.tank_index = t.Tank_index AND t.Tank_controller = l.controller_type)
                    JOIN backend_tankgroups_Tanks tgt ON tgt.tanks_id = t.Tank_id
                    JOIN backend_tankgroups tg  ON tg.Group_id = tgt.tankgroups_id
                WHERE
                    tgt.tankgroups_id IN %s
                        AND 0+l.pv BETWEEN 0.1 AND 1000000
                        AND l.read_at BETWEEN %s AND %s
                        AND l.flag_log = 1
                ORDER BY l.read_at DESC
                LIMIT %s, %s;
        """
        c.execute(query, [tuple(tankgroup_ids), start, end, offset, limit])
        data = dictfetchall(c)
    return data


def modified_get_tank_latest_log(Tank_controller, Tank_index, Controller_polling_address, mac_address, Display_unit, Unit, Capacity, Tank_name, Site_name, Product):
    with connection.cursor() as c:
        query = """
                     SELECT
                        l.pv AS Volume,
                        l.sv AS Height,
                            (CASE
                                WHEN l.temperature IS NULL THEN '0.00'
                                ELSE l.temperature
                            END) AS temperature,
                            (CASE
                                WHEN l.water IS NULL THEN '0.00'
                                ELSE l.water
                            END) AS water,
                            l.read_at AS last_updated_time
                                
                        FROM
                            atg_primary_log l
                        WHERE
                            l.Controller_type = %s
                                AND l.tank_index = %s
                                AND l.multicont_polling_address = %s
                                AND l.flag_log = 1
                                AND l.device_address= %s
                        ORDER BY l.read_at DESC
                        LIMIT 1; 
                """
        c.execute(query, (Tank_controller, Tank_index,
                  Controller_polling_address, mac_address))

        data = dictfetchall(c)
        if len(data) == 1:
            data[0]['Display Unit'] = Display_unit
            data[0]['Unit'] = Unit
            data[0]['Capacity'] = Capacity
            data[0]['Tank Name'] = Tank_name
            data[0]['siteName'] = Site_name
            data[0]['Product'] = Product
            data[0]['Tank_controller'] = Tank_controller
            data = log_utils.update_tankgroup_records(data)
        else:
            data.append(
                {
                    'Display Unit': Display_unit,
                    'Unit': Unit,
                    'Capacity': Capacity,
                    'Tank Name': Tank_name,
                    'siteName': Site_name,
                    'Tank_controller': Tank_controller,
                    'Product': Product,
                    'Fill %': 0,
                    'last_updated_time': 'N/A'

                }
            )
    return data


def get_tank_latest_log(tank_id):
    with connection.cursor() as c:
        query = """
                    SELECT 
                        sub.*,
                        ta.alarm_type AS Alarm_type,
                        (CASE
                            WHEN
                                ta.alarm_type IS NOT NULL
                                    AND (DATEDIFF(NOW(), ta.last_time_mail_sent) <= 2)
                            THEN
                                'Active'
                            ELSE 'Inactive'
                        END) AS Alarm_status
                    FROM
                        (SELECT 
                            l.pv AS Volume,
                                l.sv AS Height,
                                (CASE
                                    WHEN l.temperature IS NULL THEN '0.00'
                                    ELSE l.temperature
                                END) AS temperature,
                                (CASE
                                    WHEN l.water IS NULL THEN '0.00'
                                    ELSE l.water
                                END) AS water,
                                l.read_at AS last_updated_time,
                                l.tank_index AS tank_index,
                                l.device_address AS raspbPi_Address,
                                l.multicont_polling_address AS multicont_polling_address,
                                bs.Site_id AS Site_id,
                                bs.Name AS siteName,
                                bt.Name AS 'Tank Name',
                                bt.Tank_id AS 'Tank_id',
                                bt.Capacity AS 'Capacity',
                                bt.Reorder AS 'Reorder',
                                bt.LL_Level AS 'Deadstock',
                                bt.UOM AS 'Unit',
                                bt.Display_unit AS 'Display Unit',
                                l.controller_type AS Tank_controller,
                                bp.Name AS Product
                        FROM
                            atg_primary_log l
                        JOIN backend_devices bd ON bd.Device_unique_address = l.device_address
                        JOIN backend_sites bs ON bs.Device_id = bd.Device_id
                        JOIN backend_tanks bt ON bt.Site_id = bs.Site_id
                            AND bt.Tank_index = l.tank_index
                            AND bt.Controller_polling_address = l.multicont_polling_address
                            AND bt.Tank_controller = l.Controller_type
                        JOIN backend_products bp ON bp.Product_id = bt.Product_id
                        WHERE
                            bt.Tank_id = %s
                                AND 0 + l.pv BETWEEN 0.1 AND 1000000
                                AND l.flag_log = 1
                        ORDER BY l.read_at DESC
                        LIMIT 1) sub
                            LEFT JOIN
                        tank_alarm_dispatcher ta ON sub.`Tank_id` = ta.tank_id
                        AND ta.last_time_mail_sent = (SELECT 
                            MAX(last_time_mail_sent)
                        FROM
                            tank_alarm_dispatcher
                        WHERE
                            tank_id = sub.`Tank_id`
                        GROUP BY tank_id);
            """
        c.execute(query, (tank_id,))
        data = dictfetchall(c)
    return data


def revamped_get_tankgroup_tank_latest_log(tank_id):
    with connection.cursor() as c:
        query = """
                    SELECT 
                    Volume,
                    Height,
                    last_updated_time,
                    Site_id,
                    siteName,
                    Tank_name AS 'Tank Name',
                    Capacity,
                    Unit,
                    DisplayUnit AS 'Display Unit'
  
                FROM
                    backend_latestatglog
                WHERE
                    Tank_id = %s;
            """
        c.execute(query, (tank_id,))
        data = dictfetchall(c)
    return data


def get_tankgroup_tank_latest_log(tank_id):
    with connection.cursor() as c:
        query = """
                    SELECT 
                    l.pv AS Volume,
                    l.sv AS Height,
                    l.read_at AS last_updated_time,
                    l.tank_index AS tank_index,
                    l.multicont_polling_address AS multicont_polling_address,
                    bs.Site_id AS Site_id,
                    bs.Name AS siteName,
                    bt.Name AS 'Tank Name',
                    bt.Tank_id AS 'Tank_id',
                    bt.Capacity AS Capacity,
                    bt.UOM AS 'Unit',
                    bt.Display_unit AS 'Display Unit',
                    l.controller_type AS Tank_controller
                FROM
                    atg_primary_log l
                    JOIN backend_devices bd ON bd.Device_unique_address = l.device_address
                    JOIN backend_sites bs ON bs.Device_id = bd.Device_id
                    JOIN backend_tanks bt ON bt.Site_id = bs.Site_id and
                    bt.Tank_index = l.tank_index and bt.Controller_polling_address = l.multicont_polling_address
                    and bt.Tank_controller = l.Controller_type
                WHERE
                    bt.Tank_id = %s
                        AND 0+l.pv BETWEEN 0.1 AND 1000000
                        AND l.flag_log = 1
                ORDER BY l.read_at DESC
                LIMIT 1;
            """
        c.execute(query, (tank_id,))
        data = dictfetchall(c)
    return data


def get_tank_info(mac_address, tank_index):
    with connection.cursor() as c:
        query = """
            SELECT CalibrationChart, Offset, Density
            FROM `backend_tanks`
            WHERE `Site_id`	= (
                SELECT Site_id
                FROM `backend_sites`
                WHERE `Device_id` = (
                    SELECT Device_id
                    FROM `backend_devices`
                    WHERE Device_unique_address = %s
                )
            )
            AND `Tank_index` = %s;
        """
        c.execute(query, (mac_address, tank_index))
        data = dictfetchall(c)
    return data


def get_tanks_in_site(site_ids):
    with connection.cursor() as c:
        query = """
            SELECT s.Name as 'site_name', s.`Latitude` as latitude, s.`longitude`, d.`Device_unique_address` as device_Address, t.`Name` as tank
            FROM `backend_sites` as s
            JOIN `backend_tanks` as t
            ON s.Site_id = t.Site_id
            JOIN `backend_devices` as d
            ON s.`Device_id` = d.`Device_id`
            WHERE s.`Site_id` IN %s
            AND d.`Active` = 1;

        """
        c.execute(query, [tuple(site_ids)])
        result = dictfetchall(c)
        data = formatter(result)
    return data


def get_dashboard(site_ids):
    with connection.cursor() as c:
        query = """
        WITH ctes AS (
            SELECT
                bt.name AS tank_name,
                bt.UOM AS Display_unit,
                bt.Capacity,
                apl.pv,
                apl.temperature,
                apl.water as water,
                bp.name AS product_name,
                bs.name AS site_name,
                bs.site_id AS site_id,
                apl.read_at AS last_updated_time,
                ROW_NUMBER() OVER (
                    PARTITION BY bt.Tank_index, bt.Controller_polling_address, bd.Device_unique_address, apl.controller_type
                    ORDER BY apl.read_at DESC
                ) AS row_n
            FROM backend_tanks bt
            INNER JOIN backend_sites bs
                ON bt.Site_id = bs.Site_id
            INNER JOIN backend_devices bd
                ON bd.Device_id = bs.Device_id
            INNER JOIN atg_primary_log apl
                ON apl.device_address = bd.Device_unique_address
                AND apl.tank_index = bt.Tank_index
                AND apl.multicont_polling_address = bt.Controller_polling_address
                AND apl.controller_type = bt.Tank_controller
            INNER JOIN backend_products bp
                ON bt.Product_id = bp.Product_id
            WHERE bs.Site_id IN %s AND bt.Status=1
        )
        SELECT
            tank_name,
            Capacity,
            ROUND(pv, 2) AS pv,
            ROUND(temperature, 2) AS temperature,
            ROUND(water, 2) AS water,
            product_name,
            site_name,
            site_id,
            last_updated_time, 
            Display_unit, 
            CASE
                WHEN hour_diff > 2 AND hour_diff < 48 THEN 'offline'
                WHEN hour_diff <= 2 THEN 'active'
                ELSE 'inactive'
            END AS status
        FROM (
            SELECT *,
                TIMESTAMPDIFF(HOUR, last_updated_time, CURRENT_TIMESTAMP()) AS hour_diff
            FROM ctes
            WHERE row_n = 1
        ) AS subquery;
        """
        c.execute(query, [tuple(site_ids)])
        result = dictfetchall(c)
    return result


def get_site_device_address(site_ids):
    with connection.cursor() as c:
        query = """
            SELECT s.Name, d.`Device_unique_address` as Device, d.last_seen
            FROM `backend_sites` s
            JOIN `backend_devices`d
            ON s.`Device_id` = d.`Device_id`
            WHERE s.Site_id IN %s;
        """

        c.execute(query, [tuple(site_ids)])
        result = dictfetchall(c)
        if result:
            data = result
        else:
            data = None
    return data


def get_latest_tank_log(device_address, tank_index, multicont_polling_address, controller_type):
    with connection.cursor() as c:
        query = """SELECT * FROM atg_primary_log where device_address=%s and tank_index=%s and multicont_polling_address=%s and controller_type=%s order by read_at desc limit 1;"""
        c.execute(query, [device_address, tank_index,
                  multicont_polling_address, controller_type])
        result = dictfetchall(c)
        if result:
            data = result[0]
        else:
            data = {"pv": 0, "sv": 0, "read_at": None,
                    "temperature": 0, "water": 0}
    return data


def get_tank_id(log):

    # Get tank id model for the log instance
    device_address = log[5]
    controller_type = log[11]
    controller_address = log[6]
    tank_index = log[7]
    volume = float(log[2])
    read_at = log[1]
    log_valid = 0.1 <= volume <= 1000000
    # AND 0 + l.pv BETWEEN 0.1 AND 1000000
    if log_valid:
        try:
            # [*]I may have to exclude controller type while tracking a tank; site/device address,index,polling adrress shud be enough
            tank_reference = models.Tanks.objects.get(Site__Device__Device_unique_address=device_address,
                                                      Tank_controller=controller_type, Tank_index=tank_index, Controller_polling_address=controller_address)
            return tank_reference.Tank_id
        except Exception as e:
            pass

 # Update latest atg log for old implementation


def upadate_latest_atg_log(logs):
    if len(logs[0]) == 13:
        controller_type_locator = -2

    elif len(logs[0]) == 10:
        controller_type_locator = -1
    elif len(logs[0]) == 15:
        controller_type_locator = -4
    else:
        controller_type_locator = -1
    for log in logs:
        # Get tank model for the log instance
        device_address = log[5]
        controller_type = log[controller_type_locator]
        controller_address = log[6]
        tank_index = log[7]
        volume = float(log[2])
        read_at = log[1]
        log_valid = 0.1 <= volume <= 1000000
        # AND 0 + l.pv BETWEEN 0.1 AND 1000000
        if log_valid:
            try:
                # [*]I may have to exclude controller type while tracking a tank; site/device address,index,polling adrress shud be enough
                tank_reference = models.Tanks.objects.get(Site__Device__Device_unique_address=device_address,
                                                          Tank_controller=controller_type,
                                                          Tank_index=tank_index,
                                                          Controller_polling_address=controller_address
                                                          )
                # get contollers that capture water and temperature
                if tank_reference.Tank_controller in ['TLS']:
                    water_level = log[9]
                    temperature_level = log[10]
                else:
                    water_level = 0
                    temperature_level = 0
                tank_content_conversion = log_utils.update_tankgroup_records(
                    [{"Unit": tank_reference.UOM, "Display Unit": tank_reference.Display_unit, "Volume": volume, "Height": log[4], "Capacity": tank_reference.Capacity, "water": water_level, "temperature": temperature_level}])
                # device sends back date log sometimes, so there is need to compare date before updating obj
                x = models.LatestAtgLog.objects.filter(Q(last_updated_time__isnull=True) | Q(
                    last_updated_time__lte=read_at), Tank_id=tank_reference.Tank_id)

                if x:
                    res = x.update(
                        last_updated_time=read_at,
                        Volume=tank_content_conversion[0]['Volume'],
                        temperature=tank_content_conversion[0]['temperature'],
                        water=tank_content_conversion[0]['water'],
                        Height=tank_content_conversion[0]['Height'],
                        Fill=tank_content_conversion[0]['Fill %']
                    )
                    if tank_reference.Tank_controller == 'TLS':
                        with connection.cursor() as c:
                            query = f""" UPDATE backend_latestatglog SET last_updated_time = "{read_at}" WHERE Site_id = "{tank_reference.Site_id}" """
                            c.execute(query)

                # obj, created = models.LatestAtgLog.objects.update_or_create(last_updated_time__lte = read_at,last_updated_time__isnull = True,Tank_id=tank_reference.Tank_id,
                #         defaults={'last_updated_time': read_at, 'Volume': tank_content_conversion[0]['Volume'],
                #         'temperature':tank_content_conversion[0]['temperature'],'water':tank_content_conversion[0]['water'],'Capacity':tank_reference.Capacity,'Unit':tank_reference.UOM,'DisplayUnit':tank_reference.Display_unit,
                #         'Product':tank_reference.Product.Name,'Height':tank_content_conversion[0]['Height'],'Fill':tank_content_conversion[0]['Fill %'],'Tank_name':tank_reference.Name,'siteName':tank_reference.Site.Name,
                #         "Tank_controller":tank_reference.Tank_controller,"Reorder":tank_reference.Reorder,"LL_Level":tank_reference.LL_Level,"HH_Level":tank_reference.HH_Level
                #         }
                #     )
            except Exception as e:
                # if no tank mapping for a log instance, go to next log instance
                continue

 # Update latest atg log for new implementation


def upadate_latest_atg_log1(logs):
    for log in logs:
        # Get tank model for the log instance
        transaction_id = log[0]
        local_id = log[1]
        read_at = log[2]
        isv = float(log[5])
        sv = "{:.3f}".format(isv)
        pv_flag = log[4]
        ipv = float(log[3])
        pv = "{:.3f}".format(ipv)
        device_address = log[6]
        multicont_polling_address = log[7]
        tank_index = log[8]
        tc_volume = log[9]
        water = log[10]
        temperature = log[11]
        controller_type = log[12]
        status = log[13]
        probe_address = log[14]
        tank_id = log[15]
        try:
            x = models.Tanks.objects.filter(
                Site__Device__Device_unique_address=device_address,
                Tank_controller=controller_type,
                Tank_index=tank_index,
                Controller_polling_address=multicont_polling_address).values()
            get_prev_log_and_compare_readat_time = models.LatestAtgLog.objects.filter(Q(last_updated_time__isnull=True) | Q(
                last_updated_time__lte=read_at), Tank_id=x[0]['Tank_id'])
            if get_prev_log_and_compare_readat_time:
                if x:
                    query = f"""
                            UPDATE backend_latestatglog 
                            SET last_updated_time = "{read_at}", 
                            Volume = "{pv}", 
                            temperature = "{temperature}", 
                            water = "{water}", 
                            Height = "{sv}", 
                            Capacity = "{x[0]['Capacity']}",
                            Tank_controller = "{x[0]['Tank_controller']}"
                            WHERE Tank_id = "{x[0]['Tank_id']}";  
                        """
                    with connection.cursor() as c:
                        res = c.execute(query)
        except Exception as e:
            # if no tank mapping for a log instance, go to next log instance
            continue

# update latest atg log for PIC ATG


def update_pic_latest_atg_log(log):

    # create variables for list item
    device_address = log[0]
    multicont_polling_address = log[1]
    tank_index = log[2]
    read_at = log[3]
    pv = log[4]
    sv = log[5]
    controller_type = log[6]
    tank_id = log[7]
    transaction_id = log[8]
    try:
        tank_reference = models.Tanks.objects.get(Site__Device__Device_unique_address=device_address,
                                                  Tank_controller=controller_type,
                                                  Tank_index=tank_index,
                                                  Controller_polling_address=multicont_polling_address
                                                  )
        x = models.LatestAtgLog.objects.filter(Q(last_updated_time__isnull=True) | Q(
            last_updated_time__lte=read_at), Tank_id=tank_id).values()
        if x:
            query = f"""
                        UPDATE backend_latestatglog SET last_updated_time = "{read_at}", Volume = "{pv}", Height = "{sv}"
                        WHERE id = "{x[0]["id"]}" AND Tank_id = "{tank_id}" AND Capacity="{tank_reference.Capacity}";  
                        """
            with connection.cursor() as c:
                res = c.execute(query)
    except Exception as e:
        # if no tank mapping for a log instance, go to next log instance
        pass


def update_pic_tls_latest_atg_log(log):
    # create variables for list item
    mac_address = log[0]
    tank_index = log[1]
    pol_addr = log[2]
    read_at = log[3]
    volume = log[4]
    height = log[5]
    water = log[6]
    temperature = log[7]
    controller_type = log[8]
    tank_id = log[9]
    transaction_id = log[10]
    try:
        tank = models.Tanks.objects.filter(Tank_id=tank_id).values()
        x = models.LatestAtgLog.objects.filter(Q(last_updated_time__isnull=True) | Q(
            last_updated_time__lte=read_at), Tank_id=tank_id).values()
        if x:
            query = f"""
                        UPDATE backend_latestatglog SET last_updated_time = "{read_at}", Volume = "{volume}", Height = "{height}", water = "{water}", temperature = "{temperature}", Tank_controller = "{controller_type}"
                        WHERE id = "{x[0]["id"]}" AND Tank_id = "{tank_id}" AND Capacity = {tank[0]['Capacity']};  
                        """
            with connection.cursor() as c:
                res = c.execute(query)
    except Exception as e:
        # if no tank mapping for a log instance, go to next log instance
        pass

# Update device last seen


def update_device_online_status_for_PicAtg(log):
    read_time = datetime.datetime.now()
    device = models.Devices.objects.filter(Device_unique_address=log[0])
    if device:
        device.update(last_seen=read_time)


def upadate_latest_atg_log_sensor_logs(logs):
    for log in logs:
        # Get tank model for the log instance
        read_at = log[1]
        volume = log[2]
        flag = log[3]
        height = log[4]
        mac_address = log[5]
        controller_id = log[6]
        tank_index = log[7]
        current = log[8]
        tank_cotroller = log[9]
        tank_id = log[10]
        tank_name = log[11]
        tank_capacity = log[12]
        site = log[13]

        try:
            x = models.LatestAtgLog.objects.filter(Q(last_updated_time__isnull=True) | Q(
                last_updated_time__lte=read_at), Tank_id=tank_id).values()
            if x:
                query = f"""
                            UPDATE backend_latestatglog SET last_updated_time = "{read_at}", Volume = "{volume}", Height = "{height}", Capacity = "{tank_capacity}"
                            WHERE id = "{x[0]["id"]}" AND Tank_id = "{tank_id}";  
                            """
                with connection.cursor() as c:
                    res = c.execute(query)
        except Exception as e:
            # if no tank mapping for a log instance, go to next log instance
            tank = models.LatestAtgLog(Tank_id=tank_id, Tank_name=tank_name, last_updated_time=read_at,
                                       Volume=volume, Height=height, Capacity=tank_capacity, Site_id=site)
            tank.save()
            continue


def get_latest_tank_dashboard(site_id):
    tanks = models.Tanks.objects.filter(Site_id__in=site_id).values()
    get_devices = models.Sites.objects.filter(
        Site_id__in=site_id).values_list('Device_id', flat=True)
    get_unique_address = models.Devices.objects.filter(
        Device_id__in=get_devices).values_list('Device_unique_address', flat=True)
    # get_atg_record = models.AtgPrimaryLog.objects.filter(device_address__in=get_unique_address).values_list('tank_index',flat=True).distinct()
    # print(get_atg_record,"Records")
    query = f"""
       SELECT l.multicont_polling_address,l.device_address,l.pv AS Height,l.tank_index,l.sv AS Volume,l.read_at,l.controller_type,l.temperature,l.water FROM atg_primary_log l WHERE l.device_address = "{get_unique_address[0]}" AND l.tank_index IN (SELECT DISTINCT l.tank_index FROM atg_primary_log l WHERE device_address = "{get_unique_address[0]}") LIMIT 4
    """
    with connection.cursor() as c:
        res = c.execute(query)
        result = dictfetchall(c)

    conv_list_to_tuple = tuple(site_id)
    dashboard_detail = []

    for tank in tanks:
        # log = models.AtgPrimaryLog.objects.filter(tank_id=tank['Tank_id']).values().first()
        # site = models.Sites.objects.filter(Site_id=tank['Site_id']).values()
        # product = models.Products.objects.filter(Product_id=tank['Product_id']).values()

        # print(log)
        # if log == None:
        #     continue
        query = f"""
                    SELECT
                        s.Name AS 'siteName',
                        s.Site_id,
                        t.Name AS 'Tank_name',
                        t.Tank_id ,
                        t.Capacity AS 'Capacity',
                        t.UOM,
                        t.Display_unit AS 'DisplayUnit',
                        l.multicont_polling_address,
                        l.tank_index,
                        l.pv AS 'Volume',
                        l.sv AS 'Height',
                        l.read_at AS 'last_updated_time',
                        l.controller_type AS 'Controller_type',
                        l.temperature,
                        p.Name AS 'Product',
                        l.controller_type AS 'Tank_controller',
                        l.water AS 'water',
                        l.Status AS 'Tank_Status'
                    FROM
                        (atg_primary_log l force INDEX (indx_read_at) 
                        JOIN (backend_sites s
                        JOIN backend_devices d ON s.Device_id = d.Device_id) ON l.device_address = d.Device_unique_address
                        JOIN backend_tanks t ON s.Site_id = t.Site_id
                            AND l.multicont_polling_address = t.Controller_polling_address
                            AND l.tank_index = t.Tank_index AND t.Tank_controller = l.controller_type)
                        JOIN backend_products p ON t.Product_id = p.Product_id
                    WHERE
                        s.Site_id IN {conv_list_to_tuple} AND l.tank_id = {tank['Tank_id']}
                    ORDER BY l.read_at DESC LIMIT 1
                """
        with connection.cursor() as c:
            res = c.execute(query)
            result = dictfetchall(c)
            # print(result)
            if len(result) < 1:
                continue

        if result[0]['Controller_type'] in ['TLS']:
            water_level = result[0]['water']
            temperature_level = result[0]['temperature']
        else:
            water_level = 0
            temperature_level = 0

        tank_content_conversion = log_utils.update_tankgroup_records(
            [{"Unit": tank['UOM'], "Display Unit": tank['Display_unit'], "Volume": result[0]['Volume'], "Height": result[0]['Height'], "Capacity": tank['Capacity'], "water": water_level, "temperature": temperature_level}])

        date_time_str = log_utils.date_time_conversion(
            result[0]['last_updated_time'])
        # try:
        #     date_time_str = datetime.datetime.strptime(str(result[0]['last_updated_time']), '%Y-%m-%d %H:%M:%S')
        # except:
        #     date_time_str = result[0]['last_updated_time']

        data = {
            "Tank_id": tank['Tank_id'],
            "Tank_name": tank['Name'],
            "Volume": tank_content_conversion[0]['Volume'],
            "Height": tank_content_conversion[0]['Height'],
            "last_updated_time": date_time_str,
            "Site_id": result[0]['Site_id'],
            "siteName": result[0]['siteName'],
            "Capacity": tank['Capacity'],
            "DisplayUnit": tank['Display_unit'],
            "Product": result[0]['Product'],
            "Fill": tank_content_conversion[0]['Fill %'],
            "Tank_controller": tank['Tank_controller'],
            "temperature": tank_content_conversion[0]['temperature'],
            "water": tank_content_conversion[0]['water'],
            "Tank_Status": tank['Status'],
        }
        dashboard_detail.append(data)
    return dashboard_detail


def update_device_online_status(mac_address):
    read_time = datetime.datetime.now()
    device = models.Devices.objects.filter(Device_unique_address=mac_address)
    print(device)
    if device:
        device.update(last_seen=read_time)
