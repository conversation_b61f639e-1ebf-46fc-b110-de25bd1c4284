ERROR 2022-09-27 01:22:23,593 django.request.log_response:228- Internal Server Error: /api/v1/data_logger/
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/smartflow/smart-eye-api/venv/lib/python3.7/site-packages/django/core/handlers/exception.py", line 34, in inner
    response = get_response(request)
  File "/Users/<USER>/Desktop/smartflow/smart-eye-api/venv/lib/python3.7/site-packages/django/core/handlers/base.py", line 115, in _get_response
    response = self.process_exception_by_middleware(e, request)
  File "/Users/<USER>/Desktop/smartflow/smart-eye-api/venv/lib/python3.7/site-packages/django/core/handlers/base.py", line 113, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/Users/<USER>/Desktop/smartflow/smart-eye-api/venv/lib/python3.7/site-packages/django/views/decorators/csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "/Users/<USER>/Desktop/smartflow/smart-eye-api/venv/lib/python3.7/site-packages/django/views/generic/base.py", line 71, in view
    return self.dispatch(request, *args, **kwargs)
  File "/Users/<USER>/Desktop/smartflow/smart-eye-api/venv/lib/python3.7/site-packages/rest_framework/views.py", line 497, in dispatch
    response = self.handle_exception(exc)
  File "/Users/<USER>/Desktop/smartflow/smart-eye-api/venv/lib/python3.7/site-packages/rest_framework/views.py", line 457, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/Users/<USER>/Desktop/smartflow/smart-eye-api/venv/lib/python3.7/site-packages/rest_framework/views.py", line 468, in raise_uncaught_exception
    raise exc
  File "/Users/<USER>/Desktop/smartflow/smart-eye-api/venv/lib/python3.7/site-packages/rest_framework/views.py", line 494, in dispatch
    response = handler(request, *args, **kwargs)
  File "/Users/<USER>/Desktop/smartflow/smart-eye-api/backend/smarteye_logs/views.py", line 143, in post
    check_log = models.AtgPrimaryLog.objects.get(transaction_id=log_data[each][0])
  File "/Users/<USER>/Desktop/smartflow/smart-eye-api/venv/lib/python3.7/site-packages/django/db/models/manager.py", line 82, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/Users/<USER>/Desktop/smartflow/smart-eye-api/venv/lib/python3.7/site-packages/django/db/models/query.py", line 408, in get
    self.model._meta.object_name
backend.models.AtgPrimaryLog.DoesNotExist: AtgPrimaryLog matching query does not exist.
ERROR 2022-09-27 01:22:23,598 django.server.log_message:154- "POST /api/v1/data_logger/ HTTP/1.1" 500 27
ERROR 2022-09-27 01:30:18,759 django.request.log_response:228- Internal Server Error: /api/v1/data_logger/
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/smartflow/smart-eye-api/venv/lib/python3.7/site-packages/django/core/handlers/exception.py", line 34, in inner
    response = get_response(request)
  File "/Users/<USER>/Desktop/smartflow/smart-eye-api/venv/lib/python3.7/site-packages/django/core/handlers/base.py", line 115, in _get_response
    response = self.process_exception_by_middleware(e, request)
  File "/Users/<USER>/Desktop/smartflow/smart-eye-api/venv/lib/python3.7/site-packages/django/core/handlers/base.py", line 113, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/Users/<USER>/Desktop/smartflow/smart-eye-api/venv/lib/python3.7/site-packages/django/views/decorators/csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "/Users/<USER>/Desktop/smartflow/smart-eye-api/venv/lib/python3.7/site-packages/django/views/generic/base.py", line 71, in view
    return self.dispatch(request, *args, **kwargs)
  File "/Users/<USER>/Desktop/smartflow/smart-eye-api/venv/lib/python3.7/site-packages/rest_framework/views.py", line 497, in dispatch
    response = self.handle_exception(exc)
  File "/Users/<USER>/Desktop/smartflow/smart-eye-api/venv/lib/python3.7/site-packages/rest_framework/views.py", line 457, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/Users/<USER>/Desktop/smartflow/smart-eye-api/venv/lib/python3.7/site-packages/rest_framework/views.py", line 468, in raise_uncaught_exception
    raise exc
  File "/Users/<USER>/Desktop/smartflow/smart-eye-api/venv/lib/python3.7/site-packages/rest_framework/views.py", line 494, in dispatch
    response = handler(request, *args, **kwargs)
  File "/Users/<USER>/Desktop/smartflow/smart-eye-api/backend/smarteye_logs/views.py", line 143, in post
    check_log = models.AtgPrimaryLog.objects.get(transaction_id=log_data[each][0])
  File "/Users/<USER>/Desktop/smartflow/smart-eye-api/venv/lib/python3.7/site-packages/django/db/models/manager.py", line 82, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/Users/<USER>/Desktop/smartflow/smart-eye-api/venv/lib/python3.7/site-packages/django/db/models/query.py", line 408, in get
    self.model._meta.object_name
backend.models.AtgPrimaryLog.DoesNotExist: AtgPrimaryLog matching query does not exist.
ERROR 2022-09-27 01:30:18,762 django.server.log_message:154- "POST /api/v1/data_logger/ HTTP/1.1" 500 27
ERROR 2022-09-27 02:36:41,145 django.request.log_response:228- Internal Server Error: /api/v1/data_logger/
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/smartflow/smart-eye-api/venv/lib/python3.7/site-packages/django/core/handlers/exception.py", line 34, in inner
    response = get_response(request)
  File "/Users/<USER>/Desktop/smartflow/smart-eye-api/venv/lib/python3.7/site-packages/django/core/handlers/base.py", line 115, in _get_response
    response = self.process_exception_by_middleware(e, request)
  File "/Users/<USER>/Desktop/smartflow/smart-eye-api/venv/lib/python3.7/site-packages/django/core/handlers/base.py", line 113, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/Users/<USER>/Desktop/smartflow/smart-eye-api/venv/lib/python3.7/site-packages/django/views/decorators/csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "/Users/<USER>/Desktop/smartflow/smart-eye-api/venv/lib/python3.7/site-packages/django/views/generic/base.py", line 71, in view
    return self.dispatch(request, *args, **kwargs)
  File "/Users/<USER>/Desktop/smartflow/smart-eye-api/venv/lib/python3.7/site-packages/rest_framework/views.py", line 497, in dispatch
    response = self.handle_exception(exc)
  File "/Users/<USER>/Desktop/smartflow/smart-eye-api/venv/lib/python3.7/site-packages/rest_framework/views.py", line 457, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/Users/<USER>/Desktop/smartflow/smart-eye-api/venv/lib/python3.7/site-packages/rest_framework/views.py", line 468, in raise_uncaught_exception
    raise exc
  File "/Users/<USER>/Desktop/smartflow/smart-eye-api/venv/lib/python3.7/site-packages/rest_framework/views.py", line 494, in dispatch
    response = handler(request, *args, **kwargs)
  File "/Users/<USER>/Desktop/smartflow/smart-eye-api/backend/smarteye_logs/views.py", line 143, in post
    check_log = models.AtgPrimaryLog.objects.get(transaction_id=log_data[each][0])
  File "/Users/<USER>/Desktop/smartflow/smart-eye-api/venv/lib/python3.7/site-packages/django/db/models/manager.py", line 82, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/Users/<USER>/Desktop/smartflow/smart-eye-api/venv/lib/python3.7/site-packages/django/db/models/query.py", line 408, in get
    self.model._meta.object_name
backend.models.AtgPrimaryLog.DoesNotExist: AtgPrimaryLog matching query does not exist.
ERROR 2022-09-27 02:36:41,148 django.server.log_message:154- "POST /api/v1/data_logger/ HTTP/1.1" 500 27
ERROR 2022-09-27 02:45:09,210 django.request.log_response:228- Internal Server Error: /api/v1/data_logger/
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/smartflow/smart-eye-api/venv/lib/python3.7/site-packages/django/core/handlers/exception.py", line 34, in inner
    response = get_response(request)
  File "/Users/<USER>/Desktop/smartflow/smart-eye-api/venv/lib/python3.7/site-packages/django/core/handlers/base.py", line 115, in _get_response
    response = self.process_exception_by_middleware(e, request)
  File "/Users/<USER>/Desktop/smartflow/smart-eye-api/venv/lib/python3.7/site-packages/django/core/handlers/base.py", line 113, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/Users/<USER>/Desktop/smartflow/smart-eye-api/venv/lib/python3.7/site-packages/django/views/decorators/csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "/Users/<USER>/Desktop/smartflow/smart-eye-api/venv/lib/python3.7/site-packages/django/views/generic/base.py", line 71, in view
    return self.dispatch(request, *args, **kwargs)
  File "/Users/<USER>/Desktop/smartflow/smart-eye-api/venv/lib/python3.7/site-packages/rest_framework/views.py", line 497, in dispatch
    response = self.handle_exception(exc)
  File "/Users/<USER>/Desktop/smartflow/smart-eye-api/venv/lib/python3.7/site-packages/rest_framework/views.py", line 457, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/Users/<USER>/Desktop/smartflow/smart-eye-api/venv/lib/python3.7/site-packages/rest_framework/views.py", line 468, in raise_uncaught_exception
    raise exc
  File "/Users/<USER>/Desktop/smartflow/smart-eye-api/venv/lib/python3.7/site-packages/rest_framework/views.py", line 494, in dispatch
    response = handler(request, *args, **kwargs)
  File "/Users/<USER>/Desktop/smartflow/smart-eye-api/backend/smarteye_logs/views.py", line 143, in post
    check_log = models.AtgPrimaryLog.objects.get(transaction_id=log_data[each][0])
  File "/Users/<USER>/Desktop/smartflow/smart-eye-api/venv/lib/python3.7/site-packages/django/db/models/manager.py", line 82, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/Users/<USER>/Desktop/smartflow/smart-eye-api/venv/lib/python3.7/site-packages/django/db/models/query.py", line 408, in get
    self.model._meta.object_name
backend.models.AtgPrimaryLog.DoesNotExist: AtgPrimaryLog matching query does not exist.
ERROR 2022-09-27 02:45:09,214 django.server.log_message:154- "POST /api/v1/data_logger/ HTTP/1.1" 500 27
ERROR 2022-09-27 04:21:45,870 django.request.log_response:228- Internal Server Error: /api/v1/data_logger/
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/smartflow/smart-eye-api/venv/lib/python3.7/site-packages/django/core/handlers/exception.py", line 34, in inner
    response = get_response(request)
  File "/Users/<USER>/Desktop/smartflow/smart-eye-api/venv/lib/python3.7/site-packages/django/core/handlers/base.py", line 115, in _get_response
    response = self.process_exception_by_middleware(e, request)
  File "/Users/<USER>/Desktop/smartflow/smart-eye-api/venv/lib/python3.7/site-packages/django/core/handlers/base.py", line 113, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/Users/<USER>/Desktop/smartflow/smart-eye-api/venv/lib/python3.7/site-packages/django/views/decorators/csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "/Users/<USER>/Desktop/smartflow/smart-eye-api/venv/lib/python3.7/site-packages/django/views/generic/base.py", line 71, in view
    return self.dispatch(request, *args, **kwargs)
  File "/Users/<USER>/Desktop/smartflow/smart-eye-api/venv/lib/python3.7/site-packages/rest_framework/views.py", line 497, in dispatch
    response = self.handle_exception(exc)
  File "/Users/<USER>/Desktop/smartflow/smart-eye-api/venv/lib/python3.7/site-packages/rest_framework/views.py", line 457, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/Users/<USER>/Desktop/smartflow/smart-eye-api/venv/lib/python3.7/site-packages/rest_framework/views.py", line 468, in raise_uncaught_exception
    raise exc
  File "/Users/<USER>/Desktop/smartflow/smart-eye-api/venv/lib/python3.7/site-packages/rest_framework/views.py", line 494, in dispatch
    response = handler(request, *args, **kwargs)
  File "/Users/<USER>/Desktop/smartflow/smart-eye-api/backend/smarteye_logs/views.py", line 143, in post
    check_log = models.AtgPrimaryLog.objects.get(transaction_id=log_data[each][0])
  File "/Users/<USER>/Desktop/smartflow/smart-eye-api/venv/lib/python3.7/site-packages/django/db/models/manager.py", line 82, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/Users/<USER>/Desktop/smartflow/smart-eye-api/venv/lib/python3.7/site-packages/django/db/models/query.py", line 408, in get
    self.model._meta.object_name
backend.models.AtgPrimaryLog.DoesNotExist: AtgPrimaryLog matching query does not exist.
ERROR 2022-09-27 04:21:45,875 django.server.log_message:154- "POST /api/v1/data_logger/ HTTP/1.1" 500 27
ERROR 2022-09-27 04:30:57,014 django.request.log_response:228- Internal Server Error: /api/v1/data_logger/
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/smartflow/smart-eye-api/venv/lib/python3.7/site-packages/django/core/handlers/exception.py", line 34, in inner
    response = get_response(request)
  File "/Users/<USER>/Desktop/smartflow/smart-eye-api/venv/lib/python3.7/site-packages/django/core/handlers/base.py", line 115, in _get_response
    response = self.process_exception_by_middleware(e, request)
  File "/Users/<USER>/Desktop/smartflow/smart-eye-api/venv/lib/python3.7/site-packages/django/core/handlers/base.py", line 113, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/Users/<USER>/Desktop/smartflow/smart-eye-api/venv/lib/python3.7/site-packages/django/views/decorators/csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "/Users/<USER>/Desktop/smartflow/smart-eye-api/venv/lib/python3.7/site-packages/django/views/generic/base.py", line 71, in view
    return self.dispatch(request, *args, **kwargs)
  File "/Users/<USER>/Desktop/smartflow/smart-eye-api/venv/lib/python3.7/site-packages/rest_framework/views.py", line 497, in dispatch
    response = self.handle_exception(exc)
  File "/Users/<USER>/Desktop/smartflow/smart-eye-api/venv/lib/python3.7/site-packages/rest_framework/views.py", line 457, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/Users/<USER>/Desktop/smartflow/smart-eye-api/venv/lib/python3.7/site-packages/rest_framework/views.py", line 468, in raise_uncaught_exception
    raise exc
  File "/Users/<USER>/Desktop/smartflow/smart-eye-api/venv/lib/python3.7/site-packages/rest_framework/views.py", line 494, in dispatch
    response = handler(request, *args, **kwargs)
  File "/Users/<USER>/Desktop/smartflow/smart-eye-api/backend/smarteye_logs/views.py", line 143, in post
    check_log = models.AtgPrimaryLog.objects.get(transaction_id=log_data[each][0])
  File "/Users/<USER>/Desktop/smartflow/smart-eye-api/venv/lib/python3.7/site-packages/django/db/models/manager.py", line 82, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/Users/<USER>/Desktop/smartflow/smart-eye-api/venv/lib/python3.7/site-packages/django/db/models/query.py", line 408, in get
    self.model._meta.object_name
backend.models.AtgPrimaryLog.DoesNotExist: AtgPrimaryLog matching query does not exist.
ERROR 2022-09-27 04:30:57,018 django.server.log_message:154- "POST /api/v1/data_logger/ HTTP/1.1" 500 27
WARNING 2022-09-27 04:44:46,257 django.request.log_response:228- Unauthorized: /api/v1/tanks/
WARNING 2022-09-27 04:44:46,258 django.server.log_message:154- "GET /api/v1/tanks/ HTTP/1.1" 401 99
ERROR 2022-09-27 05:09:50,579 django.request.log_response:228- Internal Server Error: /api/v1/smartsolar/solar_data_logger/
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/smartflow/smart-eye-api/venv/lib/python3.7/site-packages/django/db/backends/utils.py", line 82, in _execute
    return self.cursor.execute(sql)
  File "/Users/<USER>/Desktop/smartflow/smart-eye-api/venv/lib/python3.7/site-packages/django/db/backends/mysql/base.py", line 71, in execute
    return self.cursor.execute(query, args)
  File "/Users/<USER>/Desktop/smartflow/smart-eye-api/venv/lib/python3.7/site-packages/MySQLdb/cursors.py", line 206, in execute
    res = self._query(query)
  File "/Users/<USER>/Desktop/smartflow/smart-eye-api/venv/lib/python3.7/site-packages/MySQLdb/cursors.py", line 319, in _query
    db.query(q)
  File "/Users/<USER>/Desktop/smartflow/smart-eye-api/venv/lib/python3.7/site-packages/MySQLdb/connections.py", line 262, in query
    _mysql.connection.query(self, query)
MySQLdb._exceptions.ProgrammingError: (1146, "Table 'smarteye_db.backend_smartsolardata' doesn't exist")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/smartflow/smart-eye-api/venv/lib/python3.7/site-packages/django/core/handlers/exception.py", line 34, in inner
    response = get_response(request)
  File "/Users/<USER>/Desktop/smartflow/smart-eye-api/venv/lib/python3.7/site-packages/django/core/handlers/base.py", line 115, in _get_response
    response = self.process_exception_by_middleware(e, request)
  File "/Users/<USER>/Desktop/smartflow/smart-eye-api/venv/lib/python3.7/site-packages/django/core/handlers/base.py", line 113, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/Users/<USER>/Desktop/smartflow/smart-eye-api/venv/lib/python3.7/site-packages/django/views/decorators/csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "/Users/<USER>/Desktop/smartflow/smart-eye-api/venv/lib/python3.7/site-packages/django/views/generic/base.py", line 71, in view
    return self.dispatch(request, *args, **kwargs)
  File "/Users/<USER>/Desktop/smartflow/smart-eye-api/venv/lib/python3.7/site-packages/rest_framework/views.py", line 497, in dispatch
    response = self.handle_exception(exc)
  File "/Users/<USER>/Desktop/smartflow/smart-eye-api/venv/lib/python3.7/site-packages/rest_framework/views.py", line 457, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/Users/<USER>/Desktop/smartflow/smart-eye-api/venv/lib/python3.7/site-packages/rest_framework/views.py", line 468, in raise_uncaught_exception
    raise exc
  File "/Users/<USER>/Desktop/smartflow/smart-eye-api/venv/lib/python3.7/site-packages/rest_framework/views.py", line 494, in dispatch
    response = handler(request, *args, **kwargs)
  File "/Users/<USER>/Desktop/smartflow/smart-eye-api/backend/smart_solar/views.py", line 23, in post
    data_set = u.get_energy_meter_or_inverter(mac_address, device_type,prev_time_log,minute,second,data[item])
  File "/Users/<USER>/Desktop/smartflow/smart-eye-api/backend/smart_solar/utils.py", line 57, in get_energy_meter_or_inverter
    update_rec = update_invater(data)
  File "/Users/<USER>/Desktop/smartflow/smart-eye-api/backend/smart_solar/queries.py", line 51, in update_invater
    c.execute(query)
  File "/Users/<USER>/Desktop/smartflow/smart-eye-api/venv/lib/python3.7/site-packages/sentry_sdk/integrations/django/__init__.py", line 446, in execute
    return real_execute(self, sql, params)
  File "/Users/<USER>/Desktop/smartflow/smart-eye-api/venv/lib/python3.7/site-packages/django/db/backends/utils.py", line 67, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "/Users/<USER>/Desktop/smartflow/smart-eye-api/venv/lib/python3.7/site-packages/django/db/backends/utils.py", line 76, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/Users/<USER>/Desktop/smartflow/smart-eye-api/venv/lib/python3.7/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "/Users/<USER>/Desktop/smartflow/smart-eye-api/venv/lib/python3.7/site-packages/django/db/utils.py", line 89, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/Users/<USER>/Desktop/smartflow/smart-eye-api/venv/lib/python3.7/site-packages/django/db/backends/utils.py", line 82, in _execute
    return self.cursor.execute(sql)
  File "/Users/<USER>/Desktop/smartflow/smart-eye-api/venv/lib/python3.7/site-packages/django/db/backends/mysql/base.py", line 71, in execute
    return self.cursor.execute(query, args)
  File "/Users/<USER>/Desktop/smartflow/smart-eye-api/venv/lib/python3.7/site-packages/MySQLdb/cursors.py", line 206, in execute
    res = self._query(query)
  File "/Users/<USER>/Desktop/smartflow/smart-eye-api/venv/lib/python3.7/site-packages/MySQLdb/cursors.py", line 319, in _query
    db.query(q)
  File "/Users/<USER>/Desktop/smartflow/smart-eye-api/venv/lib/python3.7/site-packages/MySQLdb/connections.py", line 262, in query
    _mysql.connection.query(self, query)
django.db.utils.ProgrammingError: (1146, "Table 'smarteye_db.backend_smartsolardata' doesn't exist")
ERROR 2022-09-27 05:09:50,582 django.server.log_message:154- "POST /api/v1/smartsolar/solar_data_logger/ HTTP/1.1" 500 27Internal Server Error: /api/v1/user_groups/create_groups/
Traceback (most recent call last):
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/exception.py", line 34, in inner
    response = get_response(request)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/base.py", line 115, in _get_response
    response = self.process_exception_by_middleware(e, request)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/base.py", line 113, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/views/decorators/csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/views/generic/base.py", line 71, in view
    return self.dispatch(request, *args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 497, in dispatch
    response = self.handle_exception(exc)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 457, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 468, in raise_uncaught_exception
    raise exc
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 494, in dispatch
    response = handler(request, *args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/backend/roles/views.py", line 154, in post
    print(re.match("[a-zA-Z]", request.data))
  File "/usr/lib/python3.8/re.py", line 191, in match
    return _compile(pattern, flags).match(string)
TypeError: expected string or bytes-like object
Internal Server Error: /api/v1/user_groups/create_groups/
Traceback (most recent call last):
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/exception.py", line 34, in inner
    response = get_response(request)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/base.py", line 115, in _get_response
    response = self.process_exception_by_middleware(e, request)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/base.py", line 113, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/views/decorators/csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/views/generic/base.py", line 71, in view
    return self.dispatch(request, *args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 497, in dispatch
    response = self.handle_exception(exc)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 457, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 468, in raise_uncaught_exception
    raise exc
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 494, in dispatch
    response = handler(request, *args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/backend/roles/views.py", line 154, in post
    print(re.match("[a-zA-Z]", request.data))
  File "/usr/lib/python3.8/re.py", line 191, in match
    return _compile(pattern, flags).match(string)
TypeError: expected string or bytes-like object
Bad Request: /api/v1/user_groups/create_groups/
Internal Server Error: /api/v1/user_groups/create_groups/
Traceback (most recent call last):
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/exception.py", line 34, in inner
    response = get_response(request)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/base.py", line 115, in _get_response
    response = self.process_exception_by_middleware(e, request)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/base.py", line 113, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/views/decorators/csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/views/generic/base.py", line 71, in view
    return self.dispatch(request, *args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 497, in dispatch
    response = self.handle_exception(exc)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 457, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 468, in raise_uncaught_exception
    raise exc
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 494, in dispatch
    response = handler(request, *args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/backend/roles/views.py", line 154, in post
    print(re.match("^[a-zA-Z]+$", request.data))
  File "/usr/lib/python3.8/re.py", line 191, in match
    return _compile(pattern, flags).match(string)
TypeError: expected string or bytes-like object
Bad Request: /api/v1/user_groups/create_groups/
Bad Request: /api/v1/user_groups/create_groups/
Internal Server Error: /api/v1/user_groups/create_groups/
Traceback (most recent call last):
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/exception.py", line 34, in inner
    response = get_response(request)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/base.py", line 115, in _get_response
    response = self.process_exception_by_middleware(e, request)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/base.py", line 113, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/views/decorators/csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/views/generic/base.py", line 71, in view
    return self.dispatch(request, *args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 499, in dispatch
    self.response = self.finalize_response(request, response, *args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 411, in finalize_response
    assert isinstance(response, HttpResponseBase), (
AssertionError: Expected a `Response`, `HttpResponse` or `HttpStreamingResponse` to be returned from the view, but received a `<class 'NoneType'>`
Internal Server Error: /api/v1/user_groups/create_groups/
Traceback (most recent call last):
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/exception.py", line 34, in inner
    response = get_response(request)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/base.py", line 115, in _get_response
    response = self.process_exception_by_middleware(e, request)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/base.py", line 113, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/views/decorators/csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/views/generic/base.py", line 71, in view
    return self.dispatch(request, *args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 499, in dispatch
    self.response = self.finalize_response(request, response, *args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 411, in finalize_response
    assert isinstance(response, HttpResponseBase), (
AssertionError: Expected a `Response`, `HttpResponse` or `HttpStreamingResponse` to be returned from the view, but received a `<class 'NoneType'>`
Bad Request: /api/v1/user_groups/create_groups/
Bad Request: /api/v1/user_groups/create_groups/
Bad Request: /api/v1/user_groups/create_groups/
Bad Request: /api/v1/user_groups/create_groups/
Bad Request: /api/v1/user_groups/create_groups/
Bad Request: /api/v1/user_groups/create_groups/
Bad Request: /api/v1/user_groups/create_groups/
Bad Request: /api/v1/user_groups/create_groups/
Bad Request: /api/v1/user_groups/create_groups/
Bad Request: /api/v1/user_groups/create_groups/
Bad Request: /api/v1/user_groups/create_groups/
Bad Request: /api/v1/user_groups/create_groups/
Bad Request: /api/v1/user_groups/create_groups/
Bad Request: /api/v1/user_groups/create_groups/
Internal Server Error: /api/v1/user_groups/create_groups/
Traceback (most recent call last):
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/exception.py", line 34, in inner
    response = get_response(request)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/base.py", line 115, in _get_response
    response = self.process_exception_by_middleware(e, request)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/base.py", line 113, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/views/decorators/csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/views/generic/base.py", line 71, in view
    return self.dispatch(request, *args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 497, in dispatch
    response = self.handle_exception(exc)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 457, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 468, in raise_uncaught_exception
    raise exc
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 494, in dispatch
    response = handler(request, *args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/backend/roles/views.py", line 156, in post
    print(re.match(pattern, request.data['name']))
  File "/usr/lib/python3.8/re.py", line 191, in match
    return _compile(pattern, flags).match(string)
  File "/usr/lib/python3.8/re.py", line 304, in _compile
    p = sre_compile.compile(pattern, flags)
  File "/usr/lib/python3.8/sre_compile.py", line 764, in compile
    p = sre_parse.parse(p, flags)
  File "/usr/lib/python3.8/sre_parse.py", line 948, in parse
    p = _parse_sub(source, state, flags & SRE_FLAG_VERBOSE, 0)
  File "/usr/lib/python3.8/sre_parse.py", line 443, in _parse_sub
    itemsappend(_parse(source, state, verbose, nested + 1,
  File "/usr/lib/python3.8/sre_parse.py", line 668, in _parse
    raise source.error("nothing to repeat",
re.error: nothing to repeat at position 11
Bad Request: /api/v1/user_groups/create_groups/
Bad Request: /api/v1/user_groups/create_groups/
Unprocessable Entity: /api/v1/user_groups/create_groups/
Unprocessable Entity: /api/v1/user_groups/create_groups/
Unprocessable Entity: /api/v1/user_groups/create_groups/
Unprocessable Entity: /api/v1/user_groups/create_groups/
Unprocessable Entity: /api/v1/user_groups/create_groups/
Unprocessable Entity: /api/v1/user_groups/create_groups/
Bad Request: /api/v1/user_groups/create_groups/
Bad Request: /api/v1/user_groups/create_groups/
Internal Server Error: /api/v1/user_groups/create_groups/
Traceback (most recent call last):
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/exception.py", line 34, in inner
    response = get_response(request)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/base.py", line 115, in _get_response
    response = self.process_exception_by_middleware(e, request)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/base.py", line 113, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/views/decorators/csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/views/generic/base.py", line 71, in view
    return self.dispatch(request, *args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 497, in dispatch
    response = self.handle_exception(exc)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 457, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 468, in raise_uncaught_exception
    raise exc
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 494, in dispatch
    response = handler(request, *args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/backend/roles/views.py", line 162, in post
    serializer = GroupSerializer(data= _name.lower())
AttributeError: 'dict' object has no attribute 'lower'
Internal Server Error: /api/v1/user_groups/create_groups/
Traceback (most recent call last):
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/exception.py", line 34, in inner
    response = get_response(request)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/base.py", line 115, in _get_response
    response = self.process_exception_by_middleware(e, request)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/base.py", line 113, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/views/decorators/csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/views/generic/base.py", line 71, in view
    return self.dispatch(request, *args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 497, in dispatch
    response = self.handle_exception(exc)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 457, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 468, in raise_uncaught_exception
    raise exc
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 494, in dispatch
    response = handler(request, *args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/backend/roles/views.py", line 163, in post
    if serializer.is_valid():
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/serializers.py", line 228, in is_valid
    assert hasattr(self, 'initial_data'), (
AssertionError: Cannot call `.is_valid()` as no `data=` keyword argument was passed when instantiating the serializer instance.
Unprocessable Entity: /api/v1/user_groups/create_groups/
Internal Server Error: /api/v1/user_groups/add_perm/
Traceback (most recent call last):
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/exception.py", line 34, in inner
    response = get_response(request)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/base.py", line 115, in _get_response
    response = self.process_exception_by_middleware(e, request)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/base.py", line 113, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/views/decorators/csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/views/generic/base.py", line 71, in view
    return self.dispatch(request, *args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 497, in dispatch
    response = self.handle_exception(exc)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 457, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 468, in raise_uncaught_exception
    raise exc
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 494, in dispatch
    response = handler(request, *args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/backend/roles/views.py", line 214, in post
    group.permissions.add(data['permission_names'])
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/db/models/fields/related_descriptors.py", line 936, in add
    self._add_items(
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/db/models/fields/related_descriptors.py", line 1058, in _add_items
    new_ids.add(obj)
TypeError: unhashable type: 'list'
Internal Server Error: /api/v1/user_groups/add_perm/
Traceback (most recent call last):
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/exception.py", line 34, in inner
    response = get_response(request)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/base.py", line 115, in _get_response
    response = self.process_exception_by_middleware(e, request)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/base.py", line 113, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/views/decorators/csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/views/generic/base.py", line 71, in view
    return self.dispatch(request, *args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 497, in dispatch
    response = self.handle_exception(exc)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 457, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 468, in raise_uncaught_exception
    raise exc
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 494, in dispatch
    response = handler(request, *args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/backend/roles/views.py", line 211, in post
    group.permissions.set(data['permission_names'])
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/db/models/fields/related_descriptors.py", line 997, in set
    self.add(*new_objs, through_defaults=through_defaults)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/db/models/fields/related_descriptors.py", line 936, in add
    self._add_items(
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/db/models/fields/related_descriptors.py", line 1061, in _add_items
    vals = (self.through._default_manager.using(db)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/db/models/query.py", line 892, in filter
    return self._filter_or_exclude(False, *args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/db/models/query.py", line 910, in _filter_or_exclude
    clone.query.add_q(Q(*args, **kwargs))
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/db/models/sql/query.py", line 1290, in add_q
    clause, _ = self._add_q(q_object, self.used_aliases)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/db/models/sql/query.py", line 1315, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/db/models/sql/query.py", line 1251, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/db/models/sql/query.py", line 1116, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/db/models/lookups.py", line 20, in __init__
    self.rhs = self.get_prep_lookup()
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/db/models/fields/related_lookups.py", line 59, in get_prep_lookup
    self.rhs = [target_field.get_prep_value(v) for v in self.rhs]
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/db/models/fields/related_lookups.py", line 59, in <listcomp>
    self.rhs = [target_field.get_prep_value(v) for v in self.rhs]
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/db/models/fields/__init__.py", line 972, in get_prep_value
    return int(value)
ValueError: invalid literal for int() with base 10: 'Can view companies'
Internal Server Error: /api/v1/user_groups/add_perm/
Traceback (most recent call last):
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/exception.py", line 34, in inner
    response = get_response(request)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/base.py", line 115, in _get_response
    response = self.process_exception_by_middleware(e, request)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/base.py", line 113, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/views/decorators/csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/views/generic/base.py", line 71, in view
    return self.dispatch(request, *args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 497, in dispatch
    response = self.handle_exception(exc)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 457, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 468, in raise_uncaught_exception
    raise exc
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 494, in dispatch
    response = handler(request, *args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/backend/roles/views.py", line 211, in post
    group.permissions.set(data['permission_names'])
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/db/models/fields/related_descriptors.py", line 997, in set
    self.add(*new_objs, through_defaults=through_defaults)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/db/models/fields/related_descriptors.py", line 936, in add
    self._add_items(
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/db/models/fields/related_descriptors.py", line 1061, in _add_items
    vals = (self.through._default_manager.using(db)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/db/models/query.py", line 892, in filter
    return self._filter_or_exclude(False, *args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/db/models/query.py", line 910, in _filter_or_exclude
    clone.query.add_q(Q(*args, **kwargs))
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/db/models/sql/query.py", line 1290, in add_q
    clause, _ = self._add_q(q_object, self.used_aliases)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/db/models/sql/query.py", line 1315, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/db/models/sql/query.py", line 1251, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/db/models/sql/query.py", line 1116, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/db/models/lookups.py", line 20, in __init__
    self.rhs = self.get_prep_lookup()
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/db/models/fields/related_lookups.py", line 59, in get_prep_lookup
    self.rhs = [target_field.get_prep_value(v) for v in self.rhs]
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/db/models/fields/related_lookups.py", line 59, in <listcomp>
    self.rhs = [target_field.get_prep_value(v) for v in self.rhs]
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/db/models/fields/__init__.py", line 972, in get_prep_value
    return int(value)
ValueError: invalid literal for int() with base 10: 'Can change companies'
Internal Server Error: /api/v1/user_groups/add_perm/
Traceback (most recent call last):
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/exception.py", line 34, in inner
    response = get_response(request)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/base.py", line 115, in _get_response
    response = self.process_exception_by_middleware(e, request)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/base.py", line 113, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/views/decorators/csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/views/generic/base.py", line 71, in view
    return self.dispatch(request, *args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 497, in dispatch
    response = self.handle_exception(exc)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 457, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 468, in raise_uncaught_exception
    raise exc
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 494, in dispatch
    response = handler(request, *args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/backend/roles/views.py", line 215, in post
    group.permissions.set(permission)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/db/models/fields/related_descriptors.py", line 975, in set
    objs = tuple(objs)
TypeError: 'Permission' object is not iterable
Bad Request: /api/v1/user_groups/add_perm/
Bad Request: /api/v1/user_groups/add_perm/
Bad Request: /api/v1/user_groups/add_perm/
Bad Request: /api/v1/user_groups/add_perm/
Internal Server Error: /api/v1/user_groups/add_perm/
Traceback (most recent call last):
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/exception.py", line 34, in inner
    response = get_response(request)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/base.py", line 115, in _get_response
    response = self.process_exception_by_middleware(e, request)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/base.py", line 113, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/views/decorators/csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/views/generic/base.py", line 71, in view
    return self.dispatch(request, *args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 497, in dispatch
    response = self.handle_exception(exc)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 457, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 468, in raise_uncaught_exception
    raise exc
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 494, in dispatch
    response = handler(request, *args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/backend/roles/views.py", line 201, in post
    permissions_list= data['permission_names']
KeyError: 'permission_names'
Internal Server Error: /api/v1/user_groups/add_perm/
Traceback (most recent call last):
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/exception.py", line 34, in inner
    response = get_response(request)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/base.py", line 115, in _get_response
    response = self.process_exception_by_middleware(e, request)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/base.py", line 113, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/views/decorators/csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/views/generic/base.py", line 71, in view
    return self.dispatch(request, *args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 497, in dispatch
    response = self.handle_exception(exc)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 457, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 468, in raise_uncaught_exception
    raise exc
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 494, in dispatch
    response = handler(request, *args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/backend/roles/views.py", line 201, in post
    permissions_list= data['permission_names']
KeyError: 'permission_names'
Internal Server Error: /api/v1/user_groups/add_perm/
Traceback (most recent call last):
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/exception.py", line 34, in inner
    response = get_response(request)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/base.py", line 115, in _get_response
    response = self.process_exception_by_middleware(e, request)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/base.py", line 113, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/views/decorators/csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/views/generic/base.py", line 71, in view
    return self.dispatch(request, *args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 497, in dispatch
    response = self.handle_exception(exc)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 457, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 468, in raise_uncaught_exception
    raise exc
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 494, in dispatch
    response = handler(request, *args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/backend/roles/views.py", line 201, in post
    permissions_list= data['permission_names']
KeyError: 'permission_names'
Internal Server Error: /api/v1/user_groups/add_perm/
Traceback (most recent call last):
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/exception.py", line 34, in inner
    response = get_response(request)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/base.py", line 115, in _get_response
    response = self.process_exception_by_middleware(e, request)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/base.py", line 113, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/views/decorators/csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/views/generic/base.py", line 71, in view
    return self.dispatch(request, *args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 497, in dispatch
    response = self.handle_exception(exc)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 457, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 468, in raise_uncaught_exception
    raise exc
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 494, in dispatch
    response = handler(request, *args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/backend/roles/views.py", line 201, in post
    permissions_list= data['permission_names']
KeyError: 'permission_names'
Internal Server Error: /api/v1/user_groups/add_perm/
Traceback (most recent call last):
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/exception.py", line 34, in inner
    response = get_response(request)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/base.py", line 115, in _get_response
    response = self.process_exception_by_middleware(e, request)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/base.py", line 113, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/views/decorators/csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/views/generic/base.py", line 71, in view
    return self.dispatch(request, *args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 497, in dispatch
    response = self.handle_exception(exc)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 457, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 468, in raise_uncaught_exception
    raise exc
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 494, in dispatch
    response = handler(request, *args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/backend/roles/views.py", line 201, in post
    permissions_list= data['permission_names']
KeyError: 'permission_names'
Internal Server Error: /api/v1/user_groups/add_perm/
Traceback (most recent call last):
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/exception.py", line 34, in inner
    response = get_response(request)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/base.py", line 115, in _get_response
    response = self.process_exception_by_middleware(e, request)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/base.py", line 113, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/views/decorators/csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/views/generic/base.py", line 71, in view
    return self.dispatch(request, *args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 497, in dispatch
    response = self.handle_exception(exc)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 457, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 468, in raise_uncaught_exception
    raise exc
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 494, in dispatch
    response = handler(request, *args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/backend/roles/views.py", line 201, in post
    permissions_list= data['permission_names']
KeyError: 'permission_names'
Internal Server Error: /api/v1/user_groups/add_perm/
Traceback (most recent call last):
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/exception.py", line 34, in inner
    response = get_response(request)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/base.py", line 115, in _get_response
    response = self.process_exception_by_middleware(e, request)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/base.py", line 113, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/views/decorators/csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/views/generic/base.py", line 71, in view
    return self.dispatch(request, *args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 497, in dispatch
    response = self.handle_exception(exc)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 457, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 468, in raise_uncaught_exception
    raise exc
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 494, in dispatch
    response = handler(request, *args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/backend/roles/views.py", line 201, in post
    permissions_list= data['permission_names']
KeyError: 'permission_names'
Internal Server Error: /api/v1/user_groups/add_perm/
Traceback (most recent call last):
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/exception.py", line 34, in inner
    response = get_response(request)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/base.py", line 115, in _get_response
    response = self.process_exception_by_middleware(e, request)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/base.py", line 113, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/views/decorators/csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/views/generic/base.py", line 71, in view
    return self.dispatch(request, *args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 497, in dispatch
    response = self.handle_exception(exc)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 457, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 468, in raise_uncaught_exception
    raise exc
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 494, in dispatch
    response = handler(request, *args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/backend/roles/views.py", line 201, in post
    permissions_list= data['permission_names']
KeyError: 'permission_names'
Internal Server Error: /api/v1/user_groups/add_perm/
Traceback (most recent call last):
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/exception.py", line 34, in inner
    response = get_response(request)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/base.py", line 115, in _get_response
    response = self.process_exception_by_middleware(e, request)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/base.py", line 113, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/views/decorators/csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/views/generic/base.py", line 71, in view
    return self.dispatch(request, *args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 497, in dispatch
    response = self.handle_exception(exc)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 457, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 468, in raise_uncaught_exception
    raise exc
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 494, in dispatch
    response = handler(request, *args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/backend/roles/views.py", line 201, in post
    permissions_list= data['permission_names']
KeyError: 'permission_names'
Internal Server Error: /api/v1/user_groups/add_perm/
Traceback (most recent call last):
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/exception.py", line 34, in inner
    response = get_response(request)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/base.py", line 115, in _get_response
    response = self.process_exception_by_middleware(e, request)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/base.py", line 113, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/views/decorators/csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/views/generic/base.py", line 71, in view
    return self.dispatch(request, *args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 497, in dispatch
    response = self.handle_exception(exc)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 457, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 468, in raise_uncaught_exception
    raise exc
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 494, in dispatch
    response = handler(request, *args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/backend/roles/views.py", line 201, in post
    permissions_list= data['permission_names']
KeyError: 'permission_names'
Internal Server Error: /api/v1/user_groups/add_perm/
Traceback (most recent call last):
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/exception.py", line 34, in inner
    response = get_response(request)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/base.py", line 115, in _get_response
    response = self.process_exception_by_middleware(e, request)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/base.py", line 113, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/views/decorators/csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/views/generic/base.py", line 71, in view
    return self.dispatch(request, *args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 497, in dispatch
    response = self.handle_exception(exc)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 457, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 468, in raise_uncaught_exception
    raise exc
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 494, in dispatch
    response = handler(request, *args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/backend/roles/views.py", line 201, in post
    permissions_list= data['permission_names']
KeyError: 'permission_names'
Internal Server Error: /api/v1/user_groups/add_perm/
Traceback (most recent call last):
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/exception.py", line 34, in inner
    response = get_response(request)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/base.py", line 115, in _get_response
    response = self.process_exception_by_middleware(e, request)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/base.py", line 113, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/views/decorators/csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/views/generic/base.py", line 71, in view
    return self.dispatch(request, *args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 497, in dispatch
    response = self.handle_exception(exc)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 457, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 468, in raise_uncaught_exception
    raise exc
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 494, in dispatch
    response = handler(request, *args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/backend/roles/views.py", line 201, in post
    permissions_list= data['permission_names']
KeyError: 'permission_names'
Internal Server Error: /api/v1/user_groups/add_perm/
Traceback (most recent call last):
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/exception.py", line 34, in inner
    response = get_response(request)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/base.py", line 115, in _get_response
    response = self.process_exception_by_middleware(e, request)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/base.py", line 113, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/views/decorators/csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/views/generic/base.py", line 71, in view
    return self.dispatch(request, *args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 497, in dispatch
    response = self.handle_exception(exc)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 457, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 468, in raise_uncaught_exception
    raise exc
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 494, in dispatch
    response = handler(request, *args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/backend/roles/views.py", line 201, in post
    permissions_list= data['permission_names']
KeyError: 'permission_names'
Unauthorized: /api/v1/user_groups/add_perm/
Unauthorized: /api/v1/user_groups/add_perm/
Internal Server Error: /api/v1/user_groups/add_perm/
Traceback (most recent call last):
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/exception.py", line 34, in inner
    response = get_response(request)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/base.py", line 115, in _get_response
    response = self.process_exception_by_middleware(e, request)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/base.py", line 113, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/views/decorators/csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/views/generic/base.py", line 71, in view
    return self.dispatch(request, *args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 497, in dispatch
    response = self.handle_exception(exc)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 457, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 468, in raise_uncaught_exception
    raise exc
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 494, in dispatch
    response = handler(request, *args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/backend/roles/views.py", line 200, in post
    group_name = data['group_name']
KeyError: 'group_name'
Internal Server Error: /api/v1/user_groups/add_perm/
Traceback (most recent call last):
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/exception.py", line 34, in inner
    response = get_response(request)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/base.py", line 115, in _get_response
    response = self.process_exception_by_middleware(e, request)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/base.py", line 113, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/views/decorators/csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/views/generic/base.py", line 71, in view
    return self.dispatch(request, *args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 497, in dispatch
    response = self.handle_exception(exc)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 457, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 468, in raise_uncaught_exception
    raise exc
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 494, in dispatch
    response = handler(request, *args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/backend/roles/views.py", line 201, in post
    permissions_list= data['permission_names']
KeyError: 'permission_names'
Bad Request: /api/v1/user_groups/add_perm/
Internal Server Error: /api/v1/user_groups/add_perm/
Traceback (most recent call last):
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/exception.py", line 34, in inner
    response = get_response(request)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/base.py", line 115, in _get_response
    response = self.process_exception_by_middleware(e, request)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/base.py", line 113, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/views/decorators/csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/views/generic/base.py", line 71, in view
    return self.dispatch(request, *args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 497, in dispatch
    response = self.handle_exception(exc)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 457, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 468, in raise_uncaught_exception
    raise exc
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 494, in dispatch
    response = handler(request, *args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/backend/roles/views.py", line 201, in post
    permissions_list= data['permission_names']
KeyError: 'permission_names'
Bad Request: /api/v1/user_groups/add_perm/
Internal Server Error: /api/v1/user_groups/add_perm/
Traceback (most recent call last):
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/exception.py", line 34, in inner
    response = get_response(request)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/base.py", line 115, in _get_response
    response = self.process_exception_by_middleware(e, request)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/base.py", line 113, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/views/decorators/csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/views/generic/base.py", line 71, in view
    return self.dispatch(request, *args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 497, in dispatch
    response = self.handle_exception(exc)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 457, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 468, in raise_uncaught_exception
    raise exc
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 494, in dispatch
    response = handler(request, *args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/backend/roles/views.py", line 201, in post
    permissions_list= data['permission_names']
KeyError: 'permission_names'
Internal Server Error: /api/v1/user_groups/add_perm/
Traceback (most recent call last):
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/exception.py", line 34, in inner
    response = get_response(request)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/base.py", line 115, in _get_response
    response = self.process_exception_by_middleware(e, request)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/base.py", line 113, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/views/decorators/csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/views/generic/base.py", line 71, in view
    return self.dispatch(request, *args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 497, in dispatch
    response = self.handle_exception(exc)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 457, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 468, in raise_uncaught_exception
    raise exc
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 494, in dispatch
    response = handler(request, *args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/backend/roles/views.py", line 200, in post
    group_name = data['group_name']
KeyError: 'group_name'
Internal Server Error: /api/v1/user_groups/add_perm/
Traceback (most recent call last):
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/exception.py", line 34, in inner
    response = get_response(request)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/base.py", line 115, in _get_response
    response = self.process_exception_by_middleware(e, request)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/base.py", line 113, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/views/decorators/csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/views/generic/base.py", line 71, in view
    return self.dispatch(request, *args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 497, in dispatch
    response = self.handle_exception(exc)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 457, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 468, in raise_uncaught_exception
    raise exc
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 494, in dispatch
    response = handler(request, *args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/backend/roles/views.py", line 200, in post
    group_name = data['group_name']
KeyError: 'group_name'
Internal Server Error: /api/v1/user_groups/add_perm/
Traceback (most recent call last):
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/exception.py", line 34, in inner
    response = get_response(request)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/base.py", line 115, in _get_response
    response = self.process_exception_by_middleware(e, request)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/base.py", line 113, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/views/decorators/csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/views/generic/base.py", line 71, in view
    return self.dispatch(request, *args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 497, in dispatch
    response = self.handle_exception(exc)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 457, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 468, in raise_uncaught_exception
    raise exc
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 494, in dispatch
    response = handler(request, *args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/backend/roles/views.py", line 201, in post
    permissions_list= data['permission_names']
KeyError: 'permission_names'
Bad Request: /api/v1/user_groups/add_perm/
Bad Request: /api/v1/user_groups/add_perm/
Internal Server Error: /api/v1/user_groups/add_perm/
Traceback (most recent call last):
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/exception.py", line 34, in inner
    response = get_response(request)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/base.py", line 115, in _get_response
    response = self.process_exception_by_middleware(e, request)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/base.py", line 113, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/views/decorators/csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/views/generic/base.py", line 71, in view
    return self.dispatch(request, *args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 497, in dispatch
    response = self.handle_exception(exc)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 457, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 468, in raise_uncaught_exception
    raise exc
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 494, in dispatch
    response = handler(request, *args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/backend/roles/views.py", line 200, in post
    group_name = data['group_name']
KeyError: 'group_name'
Internal Server Error: /api/v1/user_groups/add_perm/
Traceback (most recent call last):
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/exception.py", line 34, in inner
    response = get_response(request)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/base.py", line 115, in _get_response
    response = self.process_exception_by_middleware(e, request)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/base.py", line 113, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/views/decorators/csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/views/generic/base.py", line 71, in view
    return self.dispatch(request, *args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 497, in dispatch
    response = self.handle_exception(exc)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 457, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 468, in raise_uncaught_exception
    raise exc
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 494, in dispatch
    response = handler(request, *args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/backend/roles/views.py", line 200, in post
    group_name = data['group_name']
KeyError: 'group_name'
Internal Server Error: /api/v1/user_groups/add_perm/
Traceback (most recent call last):
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/exception.py", line 34, in inner
    response = get_response(request)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/base.py", line 115, in _get_response
    response = self.process_exception_by_middleware(e, request)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/base.py", line 113, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/views/decorators/csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/views/generic/base.py", line 71, in view
    return self.dispatch(request, *args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 497, in dispatch
    response = self.handle_exception(exc)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 457, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 468, in raise_uncaught_exception
    raise exc
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 494, in dispatch
    response = handler(request, *args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/backend/roles/views.py", line 201, in post
    permissions_list= data['permission_names']
KeyError: 'permission_names'
Bad Request: /api/v1/user_groups/add_perm/
Bad Request: /api/v1/user_groups/add_perm/
Bad Request: /api/v1/user_groups/add_perm/
Bad Request: /api/v1/user_groups/add_perm/
Bad Request: /api/v1/user_groups/add_perm/
Internal Server Error: /api/v1/user_groups/add_perm/
Traceback (most recent call last):
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/exception.py", line 34, in inner
    response = get_response(request)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/base.py", line 115, in _get_response
    response = self.process_exception_by_middleware(e, request)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/base.py", line 113, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/views/decorators/csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/views/generic/base.py", line 71, in view
    return self.dispatch(request, *args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 497, in dispatch
    response = self.handle_exception(exc)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 457, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 468, in raise_uncaught_exception
    raise exc
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 494, in dispatch
    response = handler(request, *args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/backend/roles/views.py", line 201, in post
    permissions_list= data['permission_names']
KeyError: 'permission_names'
Bad Request: /api/v1/user_groups/add_perm/
Internal Server Error: /api/v1/user_groups/add_perm/
Traceback (most recent call last):
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/exception.py", line 34, in inner
    response = get_response(request)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/base.py", line 115, in _get_response
    response = self.process_exception_by_middleware(e, request)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/base.py", line 113, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/views/decorators/csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/views/generic/base.py", line 71, in view
    return self.dispatch(request, *args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 497, in dispatch
    response = self.handle_exception(exc)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 457, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 468, in raise_uncaught_exception
    raise exc
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 494, in dispatch
    response = handler(request, *args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/backend/roles/views.py", line 207, in post
    print(request.body)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/request.py", line 409, in __getattr__
    return getattr(self._request, attr)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/http/request.py", line 281, in body
    raise RawPostDataException("You cannot access body after reading from request's data stream")
django.http.request.RawPostDataException: You cannot access body after reading from request's data stream
Bad Request: /api/v1/user_groups/add_perm/
Bad Request: /api/v1/user_groups/add_perm/
Bad Request: /api/v1/user_groups/add_perm/
Bad Request: /api/v1/user_groups/add_perm/
Internal Server Error: /api/v1/user_groups/add_perm/
Traceback (most recent call last):
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/exception.py", line 34, in inner
    response = get_response(request)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/base.py", line 115, in _get_response
    response = self.process_exception_by_middleware(e, request)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/base.py", line 113, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/views/decorators/csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/views/generic/base.py", line 71, in view
    return self.dispatch(request, *args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 497, in dispatch
    response = self.handle_exception(exc)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 457, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 468, in raise_uncaught_exception
    raise exc
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 494, in dispatch
    response = handler(request, *args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/backend/roles/views.py", line 202, in post
    if not request.data['group_name'] and not request.data['permission_names']:
KeyError: 'permission_names'
Internal Server Error: /api/v1/user_groups/add_perm/
Traceback (most recent call last):
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/exception.py", line 34, in inner
    response = get_response(request)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/base.py", line 115, in _get_response
    response = self.process_exception_by_middleware(e, request)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/base.py", line 113, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/views/decorators/csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/views/generic/base.py", line 71, in view
    return self.dispatch(request, *args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 497, in dispatch
    response = self.handle_exception(exc)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 457, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 468, in raise_uncaught_exception
    raise exc
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 494, in dispatch
    response = handler(request, *args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/backend/roles/views.py", line 202, in post
    if not request.data['group_name'] and not request.data['permission_names']:
KeyError: 'permission_names'
Bad Request: /api/v1/user_groups/add_perm/
Bad Request: /api/v1/user_groups/add_perm/
Internal Server Error: /api/v1/user_groups/add_perm/
Traceback (most recent call last):
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/exception.py", line 34, in inner
    response = get_response(request)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/base.py", line 115, in _get_response
    response = self.process_exception_by_middleware(e, request)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/base.py", line 113, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/views/decorators/csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/views/generic/base.py", line 71, in view
    return self.dispatch(request, *args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 497, in dispatch
    response = self.handle_exception(exc)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 457, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 468, in raise_uncaught_exception
    raise exc
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 494, in dispatch
    response = handler(request, *args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/backend/roles/views.py", line 200, in post
    group_name = data['group_name']
KeyError: 'group_name'
Internal Server Error: /api/v1/user_groups/add_perm/
Traceback (most recent call last):
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/exception.py", line 34, in inner
    response = get_response(request)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/base.py", line 115, in _get_response
    response = self.process_exception_by_middleware(e, request)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/base.py", line 113, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/views/decorators/csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/views/generic/base.py", line 71, in view
    return self.dispatch(request, *args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 497, in dispatch
    response = self.handle_exception(exc)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 457, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 468, in raise_uncaught_exception
    raise exc
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 494, in dispatch
    response = handler(request, *args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/backend/roles/views.py", line 200, in post
    group_name = data['group_name']
KeyError: 'group_name'
Internal Server Error: /api/v1/user_groups/add_perm/
Traceback (most recent call last):
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/exception.py", line 34, in inner
    response = get_response(request)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/base.py", line 115, in _get_response
    response = self.process_exception_by_middleware(e, request)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/base.py", line 113, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/views/decorators/csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/views/generic/base.py", line 71, in view
    return self.dispatch(request, *args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 497, in dispatch
    response = self.handle_exception(exc)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 457, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 468, in raise_uncaught_exception
    raise exc
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 494, in dispatch
    response = handler(request, *args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/backend/roles/views.py", line 199, in post
    group_name = request.data['group_name']
KeyError: 'group_name'
Internal Server Error: /api/v1/user_groups/add_perm/
Traceback (most recent call last):
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/exception.py", line 34, in inner
    response = get_response(request)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/base.py", line 115, in _get_response
    response = self.process_exception_by_middleware(e, request)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/base.py", line 113, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/views/decorators/csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/views/generic/base.py", line 71, in view
    return self.dispatch(request, *args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 497, in dispatch
    response = self.handle_exception(exc)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 457, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 468, in raise_uncaught_exception
    raise exc
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 494, in dispatch
    response = handler(request, *args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/backend/roles/views.py", line 199, in post
    group_name = request.data['group_name']
KeyError: 'group_name'
Internal Server Error: /api/v1/user_groups/add_perm/
Traceback (most recent call last):
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/exception.py", line 34, in inner
    response = get_response(request)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/base.py", line 115, in _get_response
    response = self.process_exception_by_middleware(e, request)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/base.py", line 113, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/views/decorators/csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/views/generic/base.py", line 71, in view
    return self.dispatch(request, *args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 497, in dispatch
    response = self.handle_exception(exc)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 457, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 468, in raise_uncaught_exception
    raise exc
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 494, in dispatch
    response = handler(request, *args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/backend/roles/views.py", line 199, in post
    if request.data['group_name']:
KeyError: 'group_name'
Bad Request: /api/v1/user_groups/add_perm/
Unauthorized: /api/v1/user_groups/add_perm/
Bad Request: /api/v1/user_groups/add_perm/
Bad Request: /api/v1/user_groups/add_perm/
Bad Request: /api/v1/user_groups/add_perm/
Bad Request: /api/v1/user_groups/add_perm/
Bad Request: /api/v1/user_groups/add_perm/
Bad Request: /api/v1/user_groups/add_perm/
Bad Request: /api/v1/user_groups/add_perm/
Bad Request: /api/v1/user_groups/add_perm/
Internal Server Error: /api/v1/user_groups/add_perm/
Traceback (most recent call last):
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/exception.py", line 34, in inner
    response = get_response(request)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/base.py", line 115, in _get_response
    response = self.process_exception_by_middleware(e, request)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/base.py", line 113, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/views/decorators/csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/views/generic/base.py", line 71, in view
    return self.dispatch(request, *args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 497, in dispatch
    response = self.handle_exception(exc)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 457, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 468, in raise_uncaught_exception
    raise exc
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 494, in dispatch
    response = handler(request, *args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/backend/roles/views.py", line 202, in post
    group_name = request.data['group_name']
KeyError: 'group_name'
Bad Request: /api/v1/user_groups/add_perm/
Internal Server Error: /api/v1/user_groups/add_perm/
Traceback (most recent call last):
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/exception.py", line 34, in inner
    response = get_response(request)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/base.py", line 115, in _get_response
    response = self.process_exception_by_middleware(e, request)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/base.py", line 113, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/views/decorators/csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/views/generic/base.py", line 71, in view
    return self.dispatch(request, *args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 497, in dispatch
    response = self.handle_exception(exc)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 457, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 468, in raise_uncaught_exception
    raise exc
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 494, in dispatch
    response = handler(request, *args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/backend/roles/views.py", line 202, in post
    group_name = request.data['group_name']
KeyError: 'group_name'
Bad Request: /api/v1/user_groups/add_perm/
Bad Request: /api/v1/user_groups/add_perm/
Bad Request: /api/v1/user_groups/add_perm/
Bad Request: /api/v1/user_groups/add_perm/
Bad Request: /api/v1/user_groups/add_perm/
Bad Request: /api/v1/user_groups/add_perm/
Bad Request: /api/v1/user_groups/add_perm/
Unauthorized: /api/v1/user_groups/remove_perm/
Internal Server Error: /api/v1/user_groups/remove_perm/
Traceback (most recent call last):
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/exception.py", line 34, in inner
    response = get_response(request)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/base.py", line 115, in _get_response
    response = self.process_exception_by_middleware(e, request)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/core/handlers/base.py", line 113, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/views/decorators/csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/django/views/generic/base.py", line 71, in view
    return self.dispatch(request, *args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 497, in dispatch
    response = self.handle_exception(exc)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 457, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 468, in raise_uncaught_exception
    raise exc
  File "/home/<USER>/Documents/smart-eye-api/venv/lib/python3.8/site-packages/rest_framework/views.py", line 494, in dispatch
    response = handler(request, *args, **kwargs)
  File "/home/<USER>/Documents/smart-eye-api/backend/roles/views.py", line 226, in delete
    group_name = data['group_name']
KeyError: 'group_name'
Forbidden: /api/v1/tanks/sitetanklist/
Forbidden: /api/v1/tanks/sitetanklist/
Not Found: /api/v1/auth/login/
Forbidden: /api/v1/tanks/sitetanklist/
Forbidden: /api/v1/tanks/sitetanklist/
Forbidden: /api/v1/tanks/sitetanklist/
Forbidden: /api/v1/tanks/sitetanklist/
WARNING 2025-08-29 21:19:30,227 django.request.log_response:230- Not Found: /api/v1/revampedtankreading/latest/
WARNING 2025-08-29 21:19:30,228 django.server.log_message:161- "GET /api/v1/revampedtankreading/latest/?site_ids=326 HTTP/1.1" 404 79
WARNING 2025-08-29 21:19:56,077 django.request.log_response:230- Not Found: /api/v1/revampedtankreading/latest/
WARNING 2025-08-29 21:19:56,078 django.server.log_message:161- "GET /api/v1/revampedtankreading/latest/?site_ids=326 HTTP/1.1" 404 79
WARNING 2025-08-29 21:21:11,660 django.request.log_response:230- Not Found: /api/v1/revampedtankreading/latest/
WARNING 2025-08-29 21:21:11,678 django.server.log_message:161- "GET /api/v1/revampedtankreading/latest/?site_ids=326 HTTP/1.1" 404 79
WARNING 2025-08-29 21:29:38,943 django.request.log_response:230- Bad Request: /api/v1/tanks/
WARNING 2025-08-29 21:29:38,945 django.server.log_message:161- "POST /api/v1/tanks/ HTTP/1.1" 400 102
WARNING 2025-08-29 21:30:08,578 django.request.log_response:230- Not Found: /api/v1/revampedtankreading/latest/
WARNING 2025-08-29 21:30:08,598 django.server.log_message:161- "GET /api/v1/revampedtankreading/latest/?site_ids=326 HTTP/1.1" 404 79
WARNING 2025-08-29 21:56:29,672 django.request.log_response:230- Bad Request: /api/v1/tanks/
WARNING 2025-08-29 21:56:29,674 django.server.log_message:161- "POST /api/v1/tanks/ HTTP/1.1" 400 121
WARNING 2025-08-29 21:56:44,375 django.request.log_response:230- Bad Request: /api/v1/tanks/
WARNING 2025-08-29 21:56:44,376 django.server.log_message:161- "POST /api/v1/tanks/ HTTP/1.1" 400 121
WARNING 2025-08-29 21:59:16,688 django.request.log_response:230- Bad Request: /api/v1/tanks/
WARNING 2025-08-29 21:59:16,688 django.server.log_message:161- "POST /api/v1/tanks/ HTTP/1.1" 400 121
WARNING 2025-08-29 22:19:19,080 django.request.log_response:230- Bad Request: /api/v1/tanks/
WARNING 2025-08-29 22:19:19,081 django.server.log_message:161- "POST /api/v1/tanks/ HTTP/1.1" 400 127
WARNING 2025-08-29 22:26:51,621 django.request.log_response:230- Not Found: /api/v1/revampedtankreading/latest/
WARNING 2025-08-29 22:26:51,647 django.server.log_message:161- "GET /api/v1/revampedtankreading/latest/?site_ids=326 HTTP/1.1" 404 79
WARNING 2025-08-30 08:44:31,953 django.request.log_response:230- Not Found: /api/v1/revampedtankreading/latest/
WARNING 2025-08-30 08:44:31,974 django.server.log_message:161- "GET /api/v1/revampedtankreading/latest/?site_ids=326 HTTP/1.1" 404 79
WARNING 2025-08-30 08:45:01,752 django.request.log_response:230- Not Found: /api/v1/revampedtankreading/latest/
WARNING 2025-08-30 08:45:01,753 django.server.log_message:161- "GET /api/v1/revampedtankreading/latest/?site_ids=275 HTTP/1.1" 404 79
WARNING 2025-08-30 08:45:26,064 django.request.log_response:230- Not Found: /api/v1/revampedtankreading/latest/
WARNING 2025-08-30 08:45:26,065 django.server.log_message:161- "GET /api/v1/revampedtankreading/latest/?site_ids=275,326 HTTP/1.1" 404 79
WARNING 2025-08-30 08:45:31,983 django.request.log_response:230- Not Found: /api/v1/revampedtankreading/latest/
WARNING 2025-08-30 08:45:31,984 django.server.log_message:161- "GET /api/v1/revampedtankreading/latest/?site_ids=275 HTTP/1.1" 404 79
WARNING 2025-08-30 08:46:19,057 django.request.log_response:230- Not Found: /api/v1/revampedtankreading/latest/
WARNING 2025-08-30 08:46:19,058 django.server.log_message:161- "GET /api/v1/revampedtankreading/latest/?site_ids=235 HTTP/1.1" 404 79
WARNING 2025-08-30 08:46:22,200 django.request.log_response:230- Not Found: /api/v1/revampedtankreading/latest/
WARNING 2025-08-30 08:46:22,201 django.server.log_message:161- "GET /api/v1/revampedtankreading/latest/?site_ids=275 HTTP/1.1" 404 79
WARNING 2025-08-30 08:46:26,799 django.request.log_response:230- Not Found: /api/v1/revampedtankreading/latest/
WARNING 2025-08-30 08:46:26,800 django.server.log_message:161- "GET /api/v1/revampedtankreading/latest/?site_ids=272 HTTP/1.1" 404 79
WARNING 2025-08-30 08:49:11,875 django.request.log_response:230- Bad Request: /api/v1/probe_chart/6/
WARNING 2025-08-30 08:49:11,876 django.server.log_message:161- "GET /api/v1/probe_chart/6/ HTTP/1.1" 400 69
WARNING 2025-08-30 16:43:01,869 django.request.log_response:230- Not Found: /api/v1/revampedtankreading/latest/
WARNING 2025-08-30 16:43:01,886 django.server.log_message:161- "GET /api/v1/revampedtankreading/latest/?site_ids=326 HTTP/1.1" 404 79
ERROR 2025-08-30 17:01:31,727 django.request.log_response:230- Internal Server Error: /api/v1/probe_chart/10/
Traceback (most recent call last):
  File "/home/<USER>/Desktop/project/smart-eye-api/venv/lib/python3.7/site-packages/django/core/handlers/exception.py", line 47, in inner
    response = get_response(request)
  File "/home/<USER>/Desktop/project/smart-eye-api/venv/lib/python3.7/site-packages/django/core/handlers/base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/home/<USER>/Desktop/project/smart-eye-api/venv/lib/python3.7/site-packages/django/views/decorators/csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "/home/<USER>/Desktop/project/smart-eye-api/venv/lib/python3.7/site-packages/django/views/generic/base.py", line 70, in view
    return self.dispatch(request, *args, **kwargs)
  File "/home/<USER>/Desktop/project/smart-eye-api/venv/lib/python3.7/site-packages/rest_framework/views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "/home/<USER>/Desktop/project/smart-eye-api/venv/lib/python3.7/site-packages/rest_framework/views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/home/<USER>/Desktop/project/smart-eye-api/venv/lib/python3.7/site-packages/rest_framework/views.py", line 480, in raise_uncaught_exception
    raise exc
  File "/home/<USER>/Desktop/project/smart-eye-api/venv/lib/python3.7/site-packages/rest_framework/views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "/home/<USER>/Desktop/project/smart-eye-api/backend/probes/views.py", line 63, in get
    json_chart = utils.convert_csv_to_json(chart_path)
  File "/home/<USER>/Desktop/project/smart-eye-api/backend/utils.py", line 63, in convert_csv_to_json
    with open(fullpath, newline='') as chart:
FileNotFoundError: [Errno 2] No such file or directory: 'probe_charts/probe_chart.csv'
ERROR 2025-08-30 17:01:31,730 django.server.log_message:161- "GET /api/v1/probe_chart/10/ HTTP/1.1" 500 22749
ERROR 2025-08-30 17:01:37,924 django.request.log_response:230- Internal Server Error: /api/v1/probe_chart/1/
Traceback (most recent call last):
  File "/home/<USER>/Desktop/project/smart-eye-api/venv/lib/python3.7/site-packages/django/core/handlers/exception.py", line 47, in inner
    response = get_response(request)
  File "/home/<USER>/Desktop/project/smart-eye-api/venv/lib/python3.7/site-packages/django/core/handlers/base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/home/<USER>/Desktop/project/smart-eye-api/venv/lib/python3.7/site-packages/django/views/decorators/csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "/home/<USER>/Desktop/project/smart-eye-api/venv/lib/python3.7/site-packages/django/views/generic/base.py", line 70, in view
    return self.dispatch(request, *args, **kwargs)
  File "/home/<USER>/Desktop/project/smart-eye-api/venv/lib/python3.7/site-packages/rest_framework/views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "/home/<USER>/Desktop/project/smart-eye-api/venv/lib/python3.7/site-packages/rest_framework/views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/home/<USER>/Desktop/project/smart-eye-api/venv/lib/python3.7/site-packages/rest_framework/views.py", line 480, in raise_uncaught_exception
    raise exc
  File "/home/<USER>/Desktop/project/smart-eye-api/venv/lib/python3.7/site-packages/rest_framework/views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "/home/<USER>/Desktop/project/smart-eye-api/backend/probes/views.py", line 63, in get
    json_chart = utils.convert_csv_to_json(chart_path)
  File "/home/<USER>/Desktop/project/smart-eye-api/backend/utils.py", line 63, in convert_csv_to_json
    with open(fullpath, newline='') as chart:
FileNotFoundError: [Errno 2] No such file or directory: 'probe_charts/analog_chart1.csv'
ERROR 2025-08-30 17:01:37,925 django.server.log_message:161- "GET /api/v1/probe_chart/1/ HTTP/1.1" 500 22749
ERROR 2025-08-30 17:11:52,741 django.request.log_response:230- Internal Server Error: /api/v1/probe_chart/1/
Traceback (most recent call last):
  File "/home/<USER>/Desktop/project/smart-eye-api/venv/lib/python3.7/site-packages/django/core/handlers/exception.py", line 47, in inner
    response = get_response(request)
  File "/home/<USER>/Desktop/project/smart-eye-api/venv/lib/python3.7/site-packages/django/core/handlers/base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/home/<USER>/Desktop/project/smart-eye-api/venv/lib/python3.7/site-packages/django/views/decorators/csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "/home/<USER>/Desktop/project/smart-eye-api/venv/lib/python3.7/site-packages/django/views/generic/base.py", line 70, in view
    return self.dispatch(request, *args, **kwargs)
  File "/home/<USER>/Desktop/project/smart-eye-api/venv/lib/python3.7/site-packages/rest_framework/views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "/home/<USER>/Desktop/project/smart-eye-api/venv/lib/python3.7/site-packages/rest_framework/views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/home/<USER>/Desktop/project/smart-eye-api/venv/lib/python3.7/site-packages/rest_framework/views.py", line 480, in raise_uncaught_exception
    raise exc
  File "/home/<USER>/Desktop/project/smart-eye-api/venv/lib/python3.7/site-packages/rest_framework/views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "/home/<USER>/Desktop/project/smart-eye-api/backend/probes/views.py", line 63, in get
    json_chart = utils.convert_csv_to_json(chart_path)
  File "/home/<USER>/Desktop/project/smart-eye-api/backend/utils.py", line 63, in convert_csv_to_json
    with open(fullpath, newline='') as chart:
FileNotFoundError: [Errno 2] No such file or directory: 'probe_charts/analog_chart1.csv'
ERROR 2025-08-30 17:11:52,743 django.server.log_message:161- "GET /api/v1/probe_chart/1/ HTTP/1.1" 500 22749
WARNING 2025-08-31 09:52:16,900 django.request.log_response:230- Not Found: /api/v1/revampedtankreading/latest/
WARNING 2025-08-31 09:52:16,901 django.server.log_message:161- "GET /api/v1/revampedtankreading/latest/?site_ids=326 HTTP/1.1" 404 79
WARNING 2025-08-31 09:55:53,448 django.request.log_response:230- Bad Request: /api/v1/tanks/
WARNING 2025-08-31 09:55:53,449 django.server.log_message:161- "POST /api/v1/tanks/ HTTP/1.1" 400 127
WARNING 2025-08-31 09:56:16,266 django.request.log_response:230- Bad Request: /api/v1/tanks/
WARNING 2025-08-31 09:56:16,267 django.server.log_message:161- "POST /api/v1/tanks/ HTTP/1.1" 400 121
WARNING 2025-08-31 10:14:48,715 django.request.log_response:230- Bad Request: /api/v1/tanks/
WARNING 2025-08-31 10:14:48,715 django.server.log_message:161- "POST /api/v1/tanks/ HTTP/1.1" 400 102
WARNING 2025-08-31 10:15:57,855 django.request.log_response:230- Bad Request: /api/v1/tanks/chart/798
WARNING 2025-08-31 10:15:57,855 django.server.log_message:161- "GET /api/v1/tanks/chart/798 HTTP/1.1" 400 83
WARNING 2025-08-31 10:16:22,217 django.request.log_response:230- Bad Request: /api/v1/tanks/chart/798
WARNING 2025-08-31 10:16:22,218 django.server.log_message:161- "GET /api/v1/tanks/chart/798 HTTP/1.1" 400 83
WARNING 2025-08-31 10:20:33,230 django.request.log_response:230- Bad Request: /api/v1/tanks/
WARNING 2025-08-31 10:20:33,230 django.server.log_message:161- "POST /api/v1/tanks/ HTTP/1.1" 400 124
WARNING 2025-08-31 10:21:36,635 django.request.log_response:230- Bad Request: /api/v1/tanks/chart/799
WARNING 2025-08-31 10:21:36,635 django.server.log_message:161- "GET /api/v1/tanks/chart/799 HTTP/1.1" 400 83
